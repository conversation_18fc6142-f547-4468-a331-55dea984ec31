//! 事件协调器
//!
//! 负责协调池管理器和套利引擎之间的事件处理，避免数据丢失

use std::str::FromStr;
use shared::pump_swap::GlobalConfig;
use state_manager::core::DexPoolManager;
use arbitrage_engine::{ArbitrageEngine, tokens, BellmanFordArbitrageEngine};
use solana_sdk::pubkey::Pubkey;
use std::sync::Arc;
use tracing::{info, warn, error};
use async_trait::async_trait;
use solana_sdk::pubkey;
use data_parser::{ParsedAccount};
use shared::raydium_cpmm::AmmConfig;
use shared::TokenMintInfo;
use state_manager::{MeteoraDammPoolManager, MeteoraLbPairState, MeteoraLbPoolManager, PumpSwapPoolManager, RaydiumClmmPoolState, RaydiumCpmmPoolManager};
use crate::event::{ArbitrageEvent, EventError, EventHandler, EventReceiver, EventResult, EventSender, PoolTokenMintUpdateEvent, PoolUpdateEvent, SystemEvent};
use crate::publish_event;

/// 事件协调器
///
/// 管理池更新事件和套利检测的协调工作
pub struct EventCoordinator {
    /// 池管理器
    pool_manager: Arc<DexPoolManager>,
    /// 套利引擎
    arbitrage_engine: Arc<BellmanFordArbitrageEngine>,
    /// 事件发送器（用于发布套利机会事件）
    event_sender: EventSender,
}

impl EventCoordinator {
    /// 创建新的事件协调器
    pub fn new(
        pool_manager: Arc<DexPoolManager>,
        arbitrage_engine: Arc<BellmanFordArbitrageEngine>,
        event_sender: EventSender,
    ) -> Self {
        Self {
            pool_manager,
            arbitrage_engine,
            event_sender,
        }
    }

    /// 处理池更新事件
    async fn handle_pool_update(&self, event: PoolUpdateEvent) -> EventResult<()> {
        let pool_address = event.pool_address;
        let account_data = event.parsed_account;

        println!("Processing pool update for {}", pool_address);

        // 获取更新前的池价格（如果存在）
        let pre_pool_price = self.pool_manager.get_pool_price(&pool_address).unwrap_or_default();

        // 更新state manager中的池状态
        update_pool_state(&self.pool_manager, &account_data).await
            .map_err(|e| EventError::ProcessingFailed(format!("Failed to update pool state: {}", e)))?;

        // 获取更新后的池价格
        let post_pool_price = self.pool_manager.get_pool_price(&pool_address)
            .map_err(|e| EventError::ProcessingFailed(format!("Failed to get updated pool state: {}", e)))?;

        println!("Pool {} price updated from {:.10} to {:.10}", pool_address, pre_pool_price, post_pool_price);


        // 检查是否需要触发套利检测
        let should_detect_arbitrage = if pre_pool_price > 0.0 {
            let price_change_pct = ((post_pool_price - pre_pool_price) / pre_pool_price * 100.0).abs();
            price_change_pct > 0.0
        } else {
            true
        };


        if should_detect_arbitrage {
            println!("检测套利机会 {}", pool_address);

            // 检测套利机会（ArbitrageEngine 会自动重建图）
            // 从 SOL 开始搜索套利机会
            match self.arbitrage_engine.detect_arbitrage_opportunities(vec![tokens::sol()]).await {
                Ok(opportunities) if !opportunities.is_empty() => {
                    info!("🎯 Found {} arbitrage opportunities", opportunities.len());
                    let arbitrage_event = ArbitrageEvent::new(opportunities);
                    let event = SystemEvent::ArbitrageOpportunity(arbitrage_event);
                    publish_event!(&self.event_sender, event);
                }
                Ok(_) => {} // 没有套利机会，不需要处理
                Err(e) => {
                    warn!("Failed to detect arbitrage opportunities: {}", e);
                }
            }
        }

        Ok(())
    }

    /// 处理套利机会事件
    async fn handle_arbitrage_opportunity(&self, event: ArbitrageEvent) -> EventResult<()> {
        let opportunities = &event.opportunities;

        if !opportunities.is_empty() {
            info!("🎯 处理套利机会事件: 发现 {} 个套利机会!", opportunities.len());

            // 显示前3个最佳机会
            for (i, opportunity) in opportunities.iter().take(3).enumerate() {
                info!("套利机会：{:?}", opportunity);
            }
        }

        Ok(())
    }

    async fn handle_pool_token_mint_update(
        &self,
        event: PoolTokenMintUpdateEvent,
    ) -> EventResult<()> {
        let pool_address = &event.pool_address.clone();

        let pool_key = Pubkey::from_str(pool_address).unwrap();

        // 获取更新前的池价格
        let pre_pool_price = self.pool_manager.get_pool_price(&pool_key).unwrap_or_default();

        // 更新池的代币 mint 信息
        self.pool_manager.update_pool_token_mint_balance(event.pool_address, event.token_mint_info)
            .map_err(|e| EventError::ProcessingFailed(format!("Failed to update pool token mint: {}", e)))?;

        // 获取更新后的池价格
        let post_pool_price = self.pool_manager.get_pool_price(&pool_key)
            .map_err(|e| EventError::ProcessingFailed(format!("Failed to get updated pool state: {}", e)))?;

        println!(
            "Token mint更新: 池 {} 的价格从 {:.10} 更新为 {:.10}",
            pool_address, pre_pool_price, post_pool_price
        );

        // 检查是否需要触发套利检测
        let should_detect_arbitrage = if pre_pool_price > 0.0 {
            let price_change_pct = ((post_pool_price - pre_pool_price) / pre_pool_price * 100.0).abs();
            price_change_pct > 0.0
        } else {
            true
        };

        if should_detect_arbitrage {
            println!("Token mint更新触发套利检测 {}", pool_address);

            // 检测套利机会（ArbitrageEngine 会自动重建图）
            // 从 SOL 开始搜索套利机会
            match self.arbitrage_engine.detect_arbitrage_opportunities(vec![tokens::sol()]).await {
                Ok(opportunities) if !opportunities.is_empty() => {
                    info!("🎯 Found {} arbitrage opportunities", opportunities.len());
                    let arbitrage_event = ArbitrageEvent::new(opportunities);
                    let event = SystemEvent::ArbitrageOpportunity(arbitrage_event);
                    publish_event!(&self.event_sender, event);
                }
                Ok(_) => {}
                Err(e) => {
                    warn!("Failed to detect arbitrage opportunities: {}", e);
                }
            }
        }

        Ok(())
    }

    /// 启动事件处理循环
    pub async fn start_event_loop(self, mut event_receiver: EventReceiver) {
        info!("事件协调器开始运行");

        while let Some(event) = event_receiver.recv().await {
            let result = match &event {
                SystemEvent::PoolUpdate(pool_event) => {
                    self.handle_pool_update(pool_event.clone()).await
                }
                SystemEvent::PoolTokenMintUpdate(token_event) => {
                    self.handle_pool_token_mint_update(token_event.clone()).await
                }
                SystemEvent::ArbitrageOpportunity(arbitrage_event) => {
                    self.handle_arbitrage_opportunity(arbitrage_event.clone()).await
                }
            };

            if let Err(e) = result {
                error!("事件处理失败 {}: {}", event, e);
            }
        }

        warn!("事件协调器退出：事件通道已关闭");
    }
}



/// 根据解析的账户数据增量更新池状态管理器
async fn update_pool_state(
    pool_manager: &Arc<DexPoolManager>,
    parsed_data: &ParsedAccount,
) -> shared::Result<()> {
    use data_parser::accounts::ParsedAccountData;

    match &parsed_data.data {
        ParsedAccountData::RaydiumPoolState { address, data } => {
            // 转换 data_parser 的 PoolState 到 state_manager 的 RaydiumClmmPoolState
            let state_manager_pool_state = convert_raydium_pool_state(*address, data);

            // 使用增量更新，如果池不存在则创建新的
            if !pool_manager.update_raydium_pool_state(*address, state_manager_pool_state.clone())? {
                // 池不存在，创建新的池管理器
                let raydium_manager = state_manager::dex::raydium::clmm::RaydiumClmmPoolManager::new(state_manager_pool_state);
                pool_manager.add_pool(raydium_manager)?;
            }
            Ok(())
        }
        ParsedAccountData::MeteoraLbPair { address, data } => {
            let state_manager_pool_state = data.into();

            let sol_wrapped_sol_mint = pubkey!("So11111111111111111111111111111111111111112");
            let token_x_decimals = if data.token_x_mint == sol_wrapped_sol_mint {
                9
            } else {
                6
            };
            let token_y_decimals = if data.token_y_mint == sol_wrapped_sol_mint {
                9
            } else {
                6
            };

            let state_manager_pool_state = MeteoraLbPairState {
                address: *address,
                token_x_decimals,
                token_y_decimals,
                ..state_manager_pool_state
            };

            // 使用增量更新，如果池不存在则创建新的
            if !pool_manager.update_meteora_dlmm_pool_state(*address, state_manager_pool_state.clone())? {
                // 池不存在，创建新的池管理器
                let meteora_manager = MeteoraLbPoolManager::new(state_manager_pool_state);
                pool_manager.add_pool(meteora_manager)?;
            }
            Ok(())
        }
        ParsedAccountData::PumpSwapPool { address, data } => {
            // 使用真正的增量更新：保留现有的 token_mint_info，只更新其他池状态
            // 先创建完整的管理器，然后提取状态进行增量更新
            let pump_swap_manager = PumpSwapPoolManager::from_account_data(
                *address, data.clone(), &GlobalConfig::default(), Some(1), Some(1))?;
            let pump_swap_state = pump_swap_manager.pool_state.clone();

            if !pool_manager.update_pump_swap_pool_state(*address, pump_swap_state)? {
                // 池不存在，添加新的池管理器
                pool_manager.add_pool(pump_swap_manager)?;
            }
            Ok(())
        }
        ParsedAccountData::RaydiumCpmmPoolState {address, data} => {
            // 使用真正的增量更新：保留现有的 token_mint_info，只更新其他池状态
            let raydium_manager = RaydiumCpmmPoolManager::from_account_data(
                *address, data.clone(), &AmmConfig::default(), Some(1), Some(1))?;
            let cpmm_state = raydium_manager.pool_state.clone();

            if !pool_manager.update_raydium_cpmm_pool_state(*address, cpmm_state)? {
                // 池不存在，添加新的池管理器
                pool_manager.add_pool(raydium_manager)?;
            }
            Ok(())
        }
        ParsedAccountData::MeteoraDammPool {address, data} => {
            // 使用真正的增量更新：保留现有的 token_mint_info，只更新其他池状态
            let meteora_manager = MeteoraDammPoolManager::from_account_data(
                *address, data.clone(), Some(1), Some(1), Some(6), Some(6))?;
            let damm_state = meteora_manager.pool_state.clone();

            if !pool_manager.update_meteora_damm_pool_state(*address, damm_state)? {
                // 池不存在，添加新的池管理器
                pool_manager.add_pool(meteora_manager)?;
            }
            Ok(())
        }
        _ => {
            println!("⚠️  暂不支持的账户类型: {:?}", parsed_data.data.account_type());
            Ok(())
        }
    }
}


/// 转换 data_parser 的 PoolState 到 state_manager 的 RaydiumClmmPoolState
fn convert_raydium_pool_state(
    pool_address: Pubkey,
    parser_data: &shared::anchor_types::raydium::PoolState,
) -> RaydiumClmmPoolState {
    use state_manager::dex::raydium::clmm::pool::RaydiumClmmPoolState;

    RaydiumClmmPoolState {
        pool_id: pool_address,
        amm_config: parser_data.amm_config,
        owner: parser_data.owner,
        token_mint_0: parser_data.token_mint_0,
        token_mint_1: parser_data.token_mint_1,
        token_vault_0: parser_data.token_vault_0,
        token_vault_1: parser_data.token_vault_1,
        observation_key: parser_data.observation_key,
        mint_decimals_0: parser_data.mint_decimals_0,
        mint_decimals_1: parser_data.mint_decimals_1,
        tick_spacing: parser_data.tick_spacing,
        liquidity: parser_data.liquidity,
        sqrt_price_x64: parser_data.sqrt_price_x64,
        tick_current: parser_data.tick_current,
        fee_growth_global_0_x64: parser_data.fee_growth_global_0_x64,
        fee_growth_global_1_x64: parser_data.fee_growth_global_1_x64,
        protocol_fees_token_0: parser_data.protocol_fees_token_0,
        protocol_fees_token_1: parser_data.protocol_fees_token_1,
        swap_in_amount_token_0: parser_data.swap_in_amount_token_0,
        swap_out_amount_token_1: parser_data.swap_out_amount_token_1,
        swap_in_amount_token_1: parser_data.swap_in_amount_token_1,
        swap_out_amount_token_0: parser_data.swap_out_amount_token_0,
        status: parser_data.status,
        // 转换奖励信息数组
        reward_infos: parser_data.reward_infos.iter().map(|r| state_manager::dex::raydium::clmm::types::RewardInfo {
            reward_state: r.reward_state,
            open_time: r.open_time,
            end_time: r.end_time,
            last_update_time: r.last_update_time,
            emissions_per_second_x64: r.emissions_per_second_x64,
            reward_total_emissioned: r.reward_total_emissioned,
            reward_claimed: r.reward_claimed,
            token_mint: r.token_mint,
            token_vault: r.token_vault,
            authority: r.authority,
            reward_growth_global_x64: r.reward_growth_global_x64,
        }).collect(),
        tick_array_bitmap: parser_data.tick_array_bitmap,
        total_fees_token_0: parser_data.total_fees_token_0,
        total_fees_claimed_token_0: parser_data.total_fees_claimed_token_0,
        total_fees_token_1: parser_data.total_fees_token_1,
        total_fees_claimed_token_1: parser_data.total_fees_claimed_token_1,
        fund_fees_token_0: parser_data.fund_fees_token_0,
        fund_fees_token_1: parser_data.fund_fees_token_1,
        open_time: parser_data.open_time,
        recent_epoch: parser_data.recent_epoch,
        // 添加缺失的字段，使用默认值
        fee_rate: 500, // 默认费率 0.05%
        active: parser_data.status == 1, // 状态为1时表示激活
    }
}




#[async_trait]
impl EventHandler for EventCoordinator {
    async fn handle_event(&self, event: SystemEvent) -> EventResult<()> {
        match event {
            SystemEvent::PoolUpdate(pool_event) => {
                self.handle_pool_update(pool_event).await
            }
            SystemEvent::PoolTokenMintUpdate(token_event) => {
                self.handle_pool_token_mint_update(token_event).await
            }
            SystemEvent::ArbitrageOpportunity(arbitrage_event) => {
                self.handle_arbitrage_opportunity(arbitrage_event).await
            }
        }
    }

    fn name(&self) -> &'static str {
        "EventCoordinator"
    }
}

/// 创建并启动事件协调器
pub async fn start_event_coordinator(
    pool_manager: Arc<DexPoolManager>,
    arbitrage_engine: Arc<BellmanFordArbitrageEngine>,
    event_sender: EventSender,
    event_receiver: EventReceiver,
) -> tokio::task::JoinHandle<()> {
    let coordinator = EventCoordinator::new(pool_manager, arbitrage_engine, event_sender);

    tokio::spawn(async move {
        coordinator.start_event_loop(event_receiver).await;
    })
}

/// 便捷函数：发布池更新事件
pub fn publish_pool_update_event(
    event_sender: &EventSender,
    pool_address: Pubkey,
    account_data: ParsedAccount,
) {
    let event = SystemEvent::PoolUpdate(PoolUpdateEvent::new(pool_address, account_data));
    publish_event!(event_sender, event);
}


pub fn publish_pool_token_mint_update_event(
    event_sender: &EventSender,
    token_mint_info: TokenMintInfo,
    pool_address: String
) {
    let event = SystemEvent::PoolTokenMintUpdate(PoolTokenMintUpdateEvent::new(token_mint_info, pool_address));
    publish_event!(event_sender, event);
}

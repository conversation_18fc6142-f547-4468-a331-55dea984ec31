{"version": "1.11.0", "name": "kamino_lending", "instructions": [{"name": "initLendingMarket", "accounts": [{"name": "lendingMarketOwner", "isMut": true, "isSigner": true}, {"name": "lendingMarket", "isMut": true, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}], "args": [{"name": "quoteCurrency", "type": {"array": ["u8", 32]}}]}, {"name": "updateLendingMarket", "accounts": [{"name": "lendingMarketOwner", "isMut": false, "isSigner": true}, {"name": "lendingMarket", "isMut": true, "isSigner": false}], "args": [{"name": "mode", "type": "u64"}, {"name": "value", "type": {"array": ["u8", 72]}}]}, {"name": "updateLendingMarketOwner", "accounts": [{"name": "lendingMarketOwnerCached", "isMut": false, "isSigner": true}, {"name": "lendingMarket", "isMut": true, "isSigner": false}], "args": []}, {"name": "initReserve", "accounts": [{"name": "lendingMarketOwner", "isMut": true, "isSigner": true}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "feeReceiver", "isMut": true, "isSigner": false}, {"name": "reserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "reserveCollateralSupply", "isMut": true, "isSigner": false}, {"name": "initialLiquiditySource", "isMut": true, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "liquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "initFarmsForReserve", "accounts": [{"name": "lendingMarketOwner", "isMut": true, "isSigner": true}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "farmsProgram", "isMut": false, "isSigner": false}, {"name": "farmsGlobalConfig", "isMut": false, "isSigner": false}, {"name": "farmState", "isMut": true, "isSigner": false}, {"name": "farmsVaultAuthority", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "mode", "type": "u8"}]}, {"name": "updateReserveConfig", "accounts": [{"name": "signer", "isMut": false, "isSigner": true}, {"name": "globalConfig", "isMut": false, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}], "args": [{"name": "mode", "type": {"defined": "UpdateConfigMode"}}, {"name": "value", "type": "bytes"}, {"name": "skipConfigIntegrityValidation", "type": "bool"}]}, {"name": "redeemFees", "accounts": [{"name": "reserve", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}, {"name": "reserveSupplyLiquidity", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "withdrawProtocolFee", "accounts": [{"name": "globalConfig", "isMut": false, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": false, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "feeVault", "isMut": true, "isSigner": false}, {"name": "feeCollectorAta", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "socializeLoss", "accounts": [{"name": "riskCouncil", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}]}, {"name": "socializeLossV2", "accounts": [{"name": "socializeLossAccounts", "accounts": [{"name": "riskCouncil", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "farmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "farmsProgram", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}]}, {"name": "markObligationForDeleveraging", "accounts": [{"name": "riskCouncil", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}], "args": [{"name": "autodeleverageTargetLtvPct", "type": "u8"}]}, {"name": "refreshReserve", "accounts": [{"name": "reserve", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "switchboardPriceOracle", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "switchboardTwapOracle", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "scopePrices", "isMut": false, "isSigner": false, "isOptional": true}], "args": []}, {"name": "refreshReservesBatch", "accounts": [], "args": [{"name": "skipPriceUp<PERSON>", "type": "bool"}]}, {"name": "depositReserveLiquidity", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "reserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "userSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "userDestinationCollateral", "isMut": true, "isSigner": false}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "liquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}]}, {"name": "redeemReserveCollateral", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "reserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "userSourceCollateral", "isMut": true, "isSigner": false}, {"name": "userDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "liquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}], "args": [{"name": "collateralAmount", "type": "u64"}]}, {"name": "initObligation", "accounts": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": true}, {"name": "feePayer", "isMut": true, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "seed1Account", "isMut": false, "isSigner": false}, {"name": "seed2Account", "isMut": false, "isSigner": false}, {"name": "ownerUserMetadata", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "InitObligationArgs"}}]}, {"name": "initObligationFarmsForReserve", "accounts": [{"name": "payer", "isMut": true, "isSigner": true}, {"name": "owner", "isMut": false, "isSigner": false}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "reserveFarmState", "isMut": true, "isSigner": false}, {"name": "obligationFarm", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "farmsProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "mode", "type": "u8"}]}, {"name": "refreshObligationFarmsForReserve", "accounts": [{"name": "crank", "isMut": false, "isSigner": true}, {"name": "baseAccounts", "accounts": [{"name": "obligation", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": false, "isSigner": false}, {"name": "reserveFarmState", "isMut": true, "isSigner": false}, {"name": "obligationFarmUserState", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}]}, {"name": "farmsProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "mode", "type": "u8"}]}, {"name": "refreshObligation", "accounts": [{"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "obligation", "isMut": true, "isSigner": false}], "args": []}, {"name": "depositObligationCollateral", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "depositReserve", "isMut": true, "isSigner": false}, {"name": "reserveDestinationCollateral", "isMut": true, "isSigner": false}, {"name": "userSourceCollateral", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}], "args": [{"name": "collateralAmount", "type": "u64"}]}, {"name": "depositObligationCollateralV2", "accounts": [{"name": "depositAccounts", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "depositReserve", "isMut": true, "isSigner": false}, {"name": "reserveDestinationCollateral", "isMut": true, "isSigner": false}, {"name": "userSourceCollateral", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "farmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "farmsProgram", "isMut": false, "isSigner": false}], "args": [{"name": "collateralAmount", "type": "u64"}]}, {"name": "withdrawObligationCollateral", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "withdrawR<PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "reserveSourceCollateral", "isMut": true, "isSigner": false}, {"name": "userDestinationCollateral", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}], "args": [{"name": "collateralAmount", "type": "u64"}]}, {"name": "withdrawObligationCollateralV2", "accounts": [{"name": "withdrawAccounts", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "withdrawR<PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "reserveSourceCollateral", "isMut": true, "isSigner": false}, {"name": "userDestinationCollateral", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "farmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "farmsProgram", "isMut": false, "isSigner": false}], "args": [{"name": "collateralAmount", "type": "u64"}]}, {"name": "borrowObligationLiquidity", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "borrowReserve", "isMut": true, "isSigner": false}, {"name": "borrowReserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "borrowReserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}, {"name": "userDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "referrerTokenState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}]}, {"name": "borrowObligationLiquidityV2", "accounts": [{"name": "borrowAccounts", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "borrowReserve", "isMut": true, "isSigner": false}, {"name": "borrowReserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "borrowReserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}, {"name": "userDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "referrerTokenState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "farmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "farmsProgram", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}]}, {"name": "repayObligationLiquidity", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "repayReserve", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "userSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}]}, {"name": "repayObligationLiquidityV2", "accounts": [{"name": "repayAccounts", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "repayReserve", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "userSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "farmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "farmsProgram", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}]}, {"name": "repayAndWithdrawAndRedeem", "accounts": [{"name": "repayAccounts", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "repayReserve", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "userSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "withdrawAccounts", "accounts": [{"name": "owner", "isMut": true, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "withdrawR<PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveSourceCollateral", "isMut": true, "isSigner": false}, {"name": "reserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "reserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "userDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "placeholderUserDestinationCollateral", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "liquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "collateralFarmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "debtFarmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "farmsProgram", "isMut": false, "isSigner": false}], "args": [{"name": "repayAmount", "type": "u64"}, {"name": "withdrawCollateralAmount", "type": "u64"}]}, {"name": "depositAndWithdraw", "accounts": [{"name": "depositAccounts", "accounts": [{"name": "owner", "isMut": true, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "reserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "reserveDestinationDepositCollateral", "isMut": true, "isSigner": false}, {"name": "userSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "placeholderUserDestinationCollateral", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "liquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "withdrawAccounts", "accounts": [{"name": "owner", "isMut": true, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "withdrawR<PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveSourceCollateral", "isMut": true, "isSigner": false}, {"name": "reserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "reserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "userDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "placeholderUserDestinationCollateral", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "liquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "depositFarmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "withdrawFarmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "farmsProgram", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}, {"name": "withdrawCollateralAmount", "type": "u64"}]}, {"name": "depositReserveLiquidityAndObligationCollateral", "accounts": [{"name": "owner", "isMut": true, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "reserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "reserveDestinationDepositCollateral", "isMut": true, "isSigner": false}, {"name": "userSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "placeholderUserDestinationCollateral", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "liquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}]}, {"name": "depositReserveLiquidityAndObligationCollateralV2", "accounts": [{"name": "depositAccounts", "accounts": [{"name": "owner", "isMut": true, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "reserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "reserveDestinationDepositCollateral", "isMut": true, "isSigner": false}, {"name": "userSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "placeholderUserDestinationCollateral", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "liquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "farmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "farmsProgram", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}]}, {"name": "withdrawObligationCollateralAndRedeemReserveCollateral", "accounts": [{"name": "owner", "isMut": true, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "withdrawR<PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveSourceCollateral", "isMut": true, "isSigner": false}, {"name": "reserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "reserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "userDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "placeholderUserDestinationCollateral", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "liquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}], "args": [{"name": "collateralAmount", "type": "u64"}]}, {"name": "withdrawObligationCollateralAndRedeemReserveCollateralV2", "accounts": [{"name": "withdrawAccounts", "accounts": [{"name": "owner", "isMut": true, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "withdrawR<PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveSourceCollateral", "isMut": true, "isSigner": false}, {"name": "reserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "reserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "userDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "placeholderUserDestinationCollateral", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "liquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "farmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "farmsProgram", "isMut": false, "isSigner": false}], "args": [{"name": "collateralAmount", "type": "u64"}]}, {"name": "liquidateObligationAndRedeemReserveCollateral", "accounts": [{"name": "liquidator", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "repayReserve", "isMut": true, "isSigner": false}, {"name": "repayReserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "repayReserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "withdrawR<PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "withdrawReserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "withdrawReserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "withdrawReserveCollateralSupply", "isMut": true, "isSigner": false}, {"name": "withdrawReserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "withdrawReserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}, {"name": "userSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "userDestinationCollateral", "isMut": true, "isSigner": false}, {"name": "userDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "repayLiquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "withdrawLiquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}, {"name": "minAcceptableReceivedLiquidityAmount", "type": "u64"}, {"name": "maxAllowedLtvOverridePercent", "type": "u64"}]}, {"name": "liquidateObligationAndRedeemReserveCollateralV2", "accounts": [{"name": "liquidationAccounts", "accounts": [{"name": "liquidator", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "repayReserve", "isMut": true, "isSigner": false}, {"name": "repayReserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "repayReserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "withdrawR<PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "withdrawReserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "withdrawReserveCollateralMint", "isMut": true, "isSigner": false}, {"name": "withdrawReserveCollateralSupply", "isMut": true, "isSigner": false}, {"name": "withdrawReserveLiquiditySupply", "isMut": true, "isSigner": false}, {"name": "withdrawReserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}, {"name": "userSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "userDestinationCollateral", "isMut": true, "isSigner": false}, {"name": "userDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "collateralTokenProgram", "isMut": false, "isSigner": false}, {"name": "repayLiquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "withdrawLiquidityTokenProgram", "isMut": false, "isSigner": false}, {"name": "instructionSysvarAccount", "isMut": false, "isSigner": false}]}, {"name": "collateralFarmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "debtFarmsAccounts", "accounts": [{"name": "obligationFarmUserState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "reserveFarmState", "isMut": true, "isSigner": false, "isOptional": true}]}, {"name": "farmsProgram", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}, {"name": "minAcceptableReceivedLiquidityAmount", "type": "u64"}, {"name": "maxAllowedLtvOverridePercent", "type": "u64"}]}, {"name": "flashRepayReserveLiquidity", "accounts": [{"name": "userTransferAuthority", "isMut": false, "isSigner": true}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "userSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}, {"name": "referrerTokenState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "referrerAccount", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "sysvarInfo", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}, {"name": "borrowInstructionIndex", "type": "u8"}]}, {"name": "flashBorrowReserveLiquidity", "accounts": [{"name": "userTransferAuthority", "isMut": false, "isSigner": true}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveSourceLiquidity", "isMut": true, "isSigner": false}, {"name": "userDestinationLiquidity", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}, {"name": "referrerTokenState", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "referrerAccount", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "sysvarInfo", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityAmount", "type": "u64"}]}, {"name": "requestElevationGroup", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}], "args": [{"name": "elevationGroup", "type": "u8"}]}, {"name": "initReferrerTokenState", "accounts": [{"name": "payer", "isMut": true, "isSigner": true}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": false, "isSigner": false}, {"name": "referrer", "isMut": false, "isSigner": false}, {"name": "referrerTokenState", "isMut": true, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "initUserMetadata", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "feePayer", "isMut": true, "isSigner": true}, {"name": "userMetadata", "isMut": true, "isSigner": false}, {"name": "referrerUserMetadata", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "userLookupTable", "type": "public<PERSON>ey"}]}, {"name": "withdrawReferrerFees", "accounts": [{"name": "referrer", "isMut": true, "isSigner": true}, {"name": "referrerTokenState", "isMut": true, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityMint", "isMut": false, "isSigner": false}, {"name": "reserveSupplyLiquidity", "isMut": true, "isSigner": false}, {"name": "referrerTokenAccount", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "lendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "initReferrerStateAndShortUrl", "accounts": [{"name": "referrer", "isMut": true, "isSigner": true}, {"name": "referrerState", "isMut": true, "isSigner": false}, {"name": "referrerShortUrl", "isMut": true, "isSigner": false}, {"name": "referrerUserMetadata", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "shortUrl", "type": "string"}]}, {"name": "deleteReferrerStateAndShortUrl", "accounts": [{"name": "referrer", "isMut": true, "isSigner": true}, {"name": "referrerState", "isMut": true, "isSigner": false}, {"name": "shortUrl", "isMut": true, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "setObligationOrder", "accounts": [{"name": "owner", "isMut": false, "isSigner": true}, {"name": "obligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}], "args": [{"name": "index", "type": "u8"}, {"name": "order", "type": {"defined": "ObligationOrder"}}]}, {"name": "initGlobalConfig", "accounts": [{"name": "payer", "isMut": true, "isSigner": true}, {"name": "globalConfig", "isMut": true, "isSigner": false}, {"name": "programData", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}], "args": []}, {"name": "updateGlobalConfig", "accounts": [{"name": "globalAdmin", "isMut": false, "isSigner": true}, {"name": "globalConfig", "isMut": true, "isSigner": false}], "args": [{"name": "mode", "type": {"defined": "UpdateGlobalConfigMode"}}, {"name": "value", "type": "bytes"}]}, {"name": "updateGlobalConfigAdmin", "accounts": [{"name": "pendingAdmin", "isMut": false, "isSigner": true}, {"name": "globalConfig", "isMut": true, "isSigner": false}], "args": []}, {"name": "idlMissingTypes", "accounts": [{"name": "signer", "isMut": false, "isSigner": true}, {"name": "globalConfig", "isMut": false, "isSigner": false}, {"name": "lendingMarket", "isMut": false, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}], "args": [{"name": "reserveFarmKind", "type": {"defined": "ReserveFarmKind"}}, {"name": "assetTier", "type": {"defined": "AssetTier"}}, {"name": "feeCalculation", "type": {"defined": "FeeCalculation"}}, {"name": "reserveStatus", "type": {"defined": "ReserveStatus"}}, {"name": "updateConfigMode", "type": {"defined": "UpdateConfigMode"}}, {"name": "updateLendingMarketConfigValue", "type": {"defined": "UpdateLendingMarketConfigValue"}}, {"name": "updateLendingMarketConfigMode", "type": {"defined": "UpdateLendingMarketMode"}}]}], "accounts": [{"name": "UserState", "type": {"kind": "struct", "fields": [{"name": "userId", "type": "u64"}, {"name": "farmState", "type": "public<PERSON>ey"}, {"name": "owner", "type": "public<PERSON>ey"}, {"name": "isFarmDelegated", "docs": ["Indicate if this user state is part of a delegated farm"], "type": "u8"}, {"name": "padding0", "type": {"array": ["u8", 7]}}, {"name": "rewardsTallyScaled", "docs": ["Rewards tally used for computation of gained rewards", "(scaled from `Decimal` representation)."], "type": {"array": ["u128", 10]}}, {"name": "rewardsIssuedUnclaimed", "docs": ["Number of reward tokens ready for claim"], "type": {"array": ["u64", 10]}}, {"name": "lastClaimTs", "type": {"array": ["u64", 10]}}, {"name": "activeStakeScaled", "docs": ["User stake deposited and usable, generating rewards and fees.", "(scaled from `Decimal` representation)."], "type": "u128"}, {"name": "pendingDepositStakeScaled", "docs": ["User stake deposited but not usable and not generating rewards yet.", "(scaled from `Decimal` representation)."], "type": "u128"}, {"name": "pendingDepositStakeTs", "docs": ["After this timestamp, pending user stake can be moved to user stake", "Initialized to now() + delayed user stake period"], "type": "u64"}, {"name": "pendingWithdrawalUnstakeScaled", "docs": ["User deposits unstaked, pending for withdrawal, not usable and not generating rewards.", "(scaled from `Decimal` representation)."], "type": "u128"}, {"name": "pendingWithdrawalUnstakeTs", "docs": ["After this timestamp, user can withdraw their deposit."], "type": "u64"}, {"name": "bump", "docs": ["User bump used for account address validation"], "type": "u64"}, {"name": "delegatee", "docs": ["Delegatee used for initialisation - useful to check against"], "type": "public<PERSON>ey"}, {"name": "lastStakeTs", "type": "u64"}, {"name": "padding1", "type": {"array": ["u64", 50]}}]}}, {"name": "GlobalConfig", "type": {"kind": "struct", "fields": [{"name": "globalAdmin", "docs": ["Global admin of the program"], "type": "public<PERSON>ey"}, {"name": "pendingAdmin", "docs": ["Pending admin must sign a specific transaction to become the global admin"], "type": "public<PERSON>ey"}, {"name": "feeCollector", "docs": ["Fee collector is the only allowed owner of token accounts receiving protocol fees"], "type": "public<PERSON>ey"}, {"name": "padding", "docs": ["Padding to make the struct size 1024 bytes"], "type": {"array": ["u8", 928]}}]}}, {"name": "LendingMarket", "type": {"kind": "struct", "fields": [{"name": "version", "docs": ["Version of lending market"], "type": "u64"}, {"name": "bumpSeed", "docs": ["Bump seed for derived authority address"], "type": "u64"}, {"name": "lendingMarketOwner", "docs": ["Owner authority which can add new reserves"], "type": "public<PERSON>ey"}, {"name": "lendingMarketOwnerCached", "docs": ["Temporary cache of the lending market owner, used in update_lending_market_owner"], "type": "public<PERSON>ey"}, {"name": "quoteCurrency", "docs": ["Currency market prices are quoted in", "e.g. \"USD\" null padded (`*b\"USD\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\\0\"`) or a SPL token mint pubkey"], "type": {"array": ["u8", 32]}}, {"name": "referralFeeBps", "docs": ["Referral fee for the lending market, as bps out of the total protocol fee"], "type": "u16"}, {"name": "emergencyMode", "type": "u8"}, {"name": "autodeleverageEnabled", "docs": ["Whether the obligations on this market should be subject to auto-deleveraging after deposit", "or borrow limit is crossed.", "Besides this flag, the particular reserve's flag also needs to be enabled (logical `AND`).", "**NOTE:** this also affects the individual \"target LTV\" deleveraging."], "type": "u8"}, {"name": "borrowDisabled", "type": "u8"}, {"name": "priceRefreshTriggerToMaxAgePct", "docs": ["Refresh price from oracle only if it's older than this percentage of the price max age.", "e.g. if the max age is set to 100s and this is set to 80%, the price will be refreshed if it's older than 80s.", "Price is always refreshed if this set to 0."], "type": "u8"}, {"name": "liquidationMaxDebtCloseFactorPct", "docs": ["Percentage of the total borrowed value in an obligation available for liquidation"], "type": "u8"}, {"name": "insolvencyRiskUnhealthyLtvPct", "docs": ["Minimum acceptable unhealthy LTV before max_debt_close_factor_pct becomes 100%"], "type": "u8"}, {"name": "minFullLiquidationValueThreshold", "docs": ["Minimum liquidation value threshold triggering full liquidation for an obligation"], "type": "u64"}, {"name": "maxLiquidatableDebtMarketValueAtOnce", "docs": ["Max allowed liquidation value in one ix call"], "type": "u64"}, {"name": "reserved0", "docs": ["[DEPRECATED] Global maximum unhealthy borrow value allowed for any obligation"], "type": {"array": ["u8", 8]}}, {"name": "globalAllowedBorrowValue", "docs": ["Global maximum allowed borrow value allowed for any obligation"], "type": "u64"}, {"name": "riskCouncil", "docs": ["The address of the risk council, in charge of making parameter and risk decisions on behalf of the protocol"], "type": "public<PERSON>ey"}, {"name": "reserved1", "docs": ["[DEPRECATED] Reward points multiplier per obligation type"], "type": {"array": ["u8", 8]}}, {"name": "elevationGroups", "docs": ["Elevation groups are used to group together reserves that have the same risk parameters and can bump the ltv and liquidation threshold"], "type": {"array": [{"defined": "ElevationGroup"}, 32]}}, {"name": "elevationGroupPadding", "type": {"array": ["u64", 90]}}, {"name": "minNetValueInObligationSf", "docs": ["Min net value accepted to be found in a position after any lending action in an obligation (scaled by quote currency decimals)"], "type": "u128"}, {"name": "minValueSkipLiquidationLtvChecks", "docs": ["Minimum value to enforce smallest ltv priority checks on the collateral reserves on liquidation"], "type": "u64"}, {"name": "name", "docs": ["Market name, zero-padded."], "type": {"array": ["u8", 32]}}, {"name": "minValueSkipLiquidationBfChecks", "docs": ["Minimum value to enforce highest borrow factor priority checks on the debt reserves on liquidation"], "type": "u64"}, {"name": "individualAutodeleverageMarginCallPeriodSecs", "docs": ["Time (in seconds) that must pass before liquidation is allowed on an obligation that has", "been individually marked for auto-deleveraging (by the risk council)."], "type": "u64"}, {"name": "minInitialDepositAmount", "docs": ["Minimum amount of deposit at creation of a reserve to prevent artificial inflation", "Note: this amount cannot be recovered, the ctoken associated are never minted"], "type": "u64"}, {"name": "obligationOrderExecutionEnabled", "docs": ["Whether the obligation orders should be evaluated during liquidations."], "type": "u8"}, {"name": "immutable", "docs": ["Whether the lending market is set as immutable."], "type": "u8"}, {"name": "obligationOrderCreationEnabled", "docs": ["Whether new obligation orders can be created.", "Note: updating or cancelling existing orders is *not* affected by this flag."], "type": "u8"}, {"name": "padding2", "type": {"array": ["u8", 5]}}, {"name": "padding1", "type": {"array": ["u64", 169]}}]}}, {"name": "Obligation", "docs": ["Lending market obligation state"], "type": {"kind": "struct", "fields": [{"name": "tag", "docs": ["Version of the struct"], "type": "u64"}, {"name": "lastUpdate", "docs": ["Last update to collateral, liquidity, or their market values"], "type": {"defined": "LastUpdate"}}, {"name": "lendingMarket", "docs": ["Lending market address"], "type": "public<PERSON>ey"}, {"name": "owner", "docs": ["Owner authority which can borrow liquidity"], "type": "public<PERSON>ey"}, {"name": "deposits", "docs": ["Deposited collateral for the obligation, unique by deposit reserve address"], "type": {"array": [{"defined": "ObligationCollateral"}, 8]}}, {"name": "lowestReserveDepositLiquidationLtv", "docs": ["Worst LTV for the collaterals backing the loan, represented as a percentage"], "type": "u64"}, {"name": "depositedValueSf", "docs": ["Market value of deposits (scaled fraction)"], "type": "u128"}, {"name": "borrows", "docs": ["Borrowed liquidity for the obligation, unique by borrow reserve address"], "type": {"array": [{"defined": "ObligationLiquidity"}, 5]}}, {"name": "borrowFactorAdjustedDebtValueSf", "docs": ["Risk adjusted market value of borrows/debt (sum of price * borrowed_amount * borrow_factor) (scaled fraction)"], "type": "u128"}, {"name": "borrowedAssetsMarketValueSf", "docs": ["Market value of borrows - used for max_liquidatable_borrowed_amount (scaled fraction)"], "type": "u128"}, {"name": "allowedBorrowValueSf", "docs": ["The maximum borrow value at the weighted average loan to value ratio (scaled fraction)"], "type": "u128"}, {"name": "unhealthyBorrowValueSf", "docs": ["The dangerous borrow value at the weighted average liquidation threshold (scaled fraction)"], "type": "u128"}, {"name": "depositsAssetTiers", "docs": ["The asset tier of the deposits"], "type": {"array": ["u8", 8]}}, {"name": "borrowsAssetTiers", "docs": ["The asset tier of the borrows"], "type": {"array": ["u8", 5]}}, {"name": "elevationGroup", "docs": ["The elevation group id the obligation opted into."], "type": "u8"}, {"name": "numOfObsoleteDepositReserves", "docs": ["The number of obsolete reserves the obligation has a deposit in"], "type": "u8"}, {"name": "hasDebt", "docs": ["Marked = 1 if borrows array is not empty, 0 = borrows empty"], "type": "u8"}, {"name": "referrer", "docs": ["Wallet address of the referrer"], "type": "public<PERSON>ey"}, {"name": "borrowingDisabled", "docs": ["Marked = 1 if borrowing disabled, 0 = borrowing enabled"], "type": "u8"}, {"name": "autodeleverageTargetLtvPct", "docs": ["A target LTV set by the risk council when marking this obligation for deleveraging.", "Only effective when `deleveraging_margin_call_started_slot != 0`."], "type": "u8"}, {"name": "lowestReserveDepositMaxLtvPct", "docs": ["The lowest max LTV found amongst the collateral deposits"], "type": "u8"}, {"name": "numOfObsoleteBorrowReserves", "docs": ["The number of obsolete reserves the obligation has a borrow in"], "type": "u8"}, {"name": "reserved", "type": {"array": ["u8", 4]}}, {"name": "highestBorrowFactorPct", "type": "u64"}, {"name": "autodeleverageMarginCallStartedTimestamp", "docs": ["A timestamp at which the risk council most-recently marked this obligation for deleveraging.", "Zero if not currently subject to deleveraging."], "type": "u64"}, {"name": "orders", "docs": ["Owner-defined, liquidator-executed orders applicable to this obligation.", "Typical use-cases would be a stop-loss and a take-profit (possibly co-existing)."], "type": {"array": [{"defined": "ObligationOrder"}, 2]}}, {"name": "padding3", "type": {"array": ["u64", 93]}}]}}, {"name": "ReferrerState", "type": {"kind": "struct", "fields": [{"name": "shortUrl", "type": "public<PERSON>ey"}, {"name": "owner", "type": "public<PERSON>ey"}]}}, {"name": "ReferrerTokenState", "docs": ["Referrer account -> each owner can have multiple accounts for specific reserves"], "type": {"kind": "struct", "fields": [{"name": "referrer", "docs": ["Pubkey of the referrer/owner"], "type": "public<PERSON>ey"}, {"name": "mint", "docs": ["Token mint for the account"], "type": "public<PERSON>ey"}, {"name": "amountUnclaimedSf", "docs": ["Amount that has been accumulated and not claimed yet -> available to claim (scaled fraction)"], "type": "u128"}, {"name": "amountCumulativeSf", "docs": ["Amount that has been accumulated in total -> both already claimed and unclaimed (scaled fraction)"], "type": "u128"}, {"name": "bump", "docs": ["Referrer token state bump, used for address validation"], "type": "u64"}, {"name": "padding", "type": {"array": ["u64", 31]}}]}}, {"name": "ShortUrl", "type": {"kind": "struct", "fields": [{"name": "referrer", "type": "public<PERSON>ey"}, {"name": "shortUrl", "type": "string"}]}}, {"name": "UserMetadata", "docs": ["Referrer account -> each owner can have multiple accounts for specific reserves"], "type": {"kind": "struct", "fields": [{"name": "referrer", "docs": ["Pubkey of the referrer/owner - pubkey::default if no referrer"], "type": "public<PERSON>ey"}, {"name": "bump", "docs": ["Bump used for validation of account address"], "type": "u64"}, {"name": "userLookupTable", "docs": ["User lookup table - used to store all user accounts - atas for each reserve mint, each obligation PDA, UserMetadata itself and all referrer_token_states if there is a referrer"], "type": "public<PERSON>ey"}, {"name": "owner", "docs": ["User metadata account owner"], "type": "public<PERSON>ey"}, {"name": "padding1", "type": {"array": ["u64", 51]}}, {"name": "padding2", "type": {"array": ["u64", 64]}}]}}, {"name": "Reserve", "type": {"kind": "struct", "fields": [{"name": "version", "docs": ["Version of the reserve"], "type": "u64"}, {"name": "lastUpdate", "docs": ["Last slot when supply and rates updated"], "type": {"defined": "LastUpdate"}}, {"name": "lendingMarket", "docs": ["Lending market address"], "type": "public<PERSON>ey"}, {"name": "farmCollateral", "type": "public<PERSON>ey"}, {"name": "farmDebt", "type": "public<PERSON>ey"}, {"name": "liquidity", "docs": ["Reserve liquidity"], "type": {"defined": "ReserveLiquidity"}}, {"name": "reserveLiquidityPadding", "type": {"array": ["u64", 150]}}, {"name": "collateral", "docs": ["Reserve collateral"], "type": {"defined": "ReserveCollateral"}}, {"name": "reserveCollateralPadding", "type": {"array": ["u64", 150]}}, {"name": "config", "docs": ["Reserve configuration values"], "type": {"defined": "ReserveConfig"}}, {"name": "configPadding", "type": {"array": ["u64", 116]}}, {"name": "borrowedAmountOutsideElevationGroup", "type": "u64"}, {"name": "borrowedAmountsAgainstThisReserveInElevationGroups", "docs": ["Amount of token borrowed in lamport of debt asset in the given", "elevation group when this reserve is part of the collaterals."], "type": {"array": ["u64", 32]}}, {"name": "padding", "type": {"array": ["u64", 207]}}]}}], "types": [{"name": "UpdateConfigMode", "type": {"kind": "enum", "variants": [{"name": "UpdateLoanToValuePct"}, {"name": "UpdateMaxLiquidationBonusBps"}, {"name": "UpdateLiquidationThresholdPct"}, {"name": "UpdateProtocolLiquidationFee"}, {"name": "UpdateProtocolTakeRate"}, {"name": "UpdateFeesBorrowFee"}, {"name": "UpdateFeesFlashLoanFee"}, {"name": "DeprecatedUpdateFeesReferralFeeBps"}, {"name": "UpdateDepositLimit"}, {"name": "UpdateBorrowLimit"}, {"name": "UpdateTokenInfoLowerHeuristic"}, {"name": "UpdateTokenInfoUpperHeuristic"}, {"name": "UpdateTokenInfoExpHeuristic"}, {"name": "UpdateTokenInfoTwapDivergence"}, {"name": "UpdateTokenInfoScopeTwap"}, {"name": "UpdateTokenInfoScopeChain"}, {"name": "UpdateTokenInfoName"}, {"name": "UpdateTokenInfoPriceMaxAge"}, {"name": "UpdateTokenInfoTwapMaxAge"}, {"name": "UpdateScopePriceFeed"}, {"name": "UpdatePythPrice"}, {"name": "UpdateSwitchboardFeed"}, {"name": "UpdateSwitchboardTwapFeed"}, {"name": "UpdateBorrowRateCurve"}, {"name": "UpdateEntireReserveConfig"}, {"name": "UpdateDebtWithdrawalCap"}, {"name": "UpdateDepositWithdrawalCap"}, {"name": "DeprecatedUpdateDebtWithdrawalCapCurrentTotal"}, {"name": "DeprecatedUpdateDepositWithdrawalCapCurrentTotal"}, {"name": "UpdateBadDebtLiquidationBonusBps"}, {"name": "UpdateMinLiquidationBonusBps"}, {"name": "UpdateDeleveragingMarginCallPeriod"}, {"name": "UpdateBorrowFactor"}, {"name": "UpdateAssetTier"}, {"name": "UpdateElevationGroup"}, {"name": "UpdateDeleveragingThresholdDecreaseBpsPerDay"}, {"name": "DeprecatedUpdateMultiplierSideBoost"}, {"name": "DeprecatedUpdateMultiplierTagBoost"}, {"name": "UpdateReserveStatus"}, {"name": "UpdateFarmCollateral"}, {"name": "UpdateFarmDebt"}, {"name": "UpdateDisableUsageAsCollateralOutsideEmode"}, {"name": "UpdateBlockBorrowingAboveUtilizationPct"}, {"name": "UpdateBlockPriceUsage"}, {"name": "UpdateBorrowLimitOutsideElevationGroup"}, {"name": "UpdateBorrowLimitsInElevationGroupAgainstThisReserve"}, {"name": "UpdateHostFixedInterestRateBps"}, {"name": "UpdateAutodeleverageEnabled"}, {"name": "UpdateDeleveragingBonusIncreaseBpsPerDay"}, {"name": "UpdateProtocolOrderExecutionFee"}]}}, {"name": "UpdateLendingMarketConfigValue", "type": {"kind": "enum", "variants": [{"name": "Bool", "fields": ["bool"]}, {"name": "U8", "fields": ["u8"]}, {"name": "U8Array", "fields": [{"array": ["u8", 8]}]}, {"name": "U16", "fields": ["u16"]}, {"name": "U64", "fields": ["u64"]}, {"name": "U128", "fields": ["u128"]}, {"name": "Pubkey", "fields": ["public<PERSON>ey"]}, {"name": "ElevationGroup", "fields": [{"defined": "ElevationGroup"}]}, {"name": "Name", "fields": [{"array": ["u8", 32]}]}]}}, {"name": "UpdateLendingMarketMode", "type": {"kind": "enum", "variants": [{"name": "UpdateOwner"}, {"name": "UpdateEmergencyMode"}, {"name": "UpdateLiquidationCloseFactor"}, {"name": "UpdateLiquidationMaxValue"}, {"name": "DeprecatedUpdateGlobalUnhealthyBorrow"}, {"name": "UpdateGlobalAllowedBorrow"}, {"name": "UpdateRiskCouncil"}, {"name": "UpdateMinFullLiquidationThreshold"}, {"name": "UpdateInsolvencyRiskLtv"}, {"name": "UpdateElevationGroup"}, {"name": "UpdateReferralFeeBps"}, {"name": "DeprecatedUpdateMultiplierPoints"}, {"name": "UpdatePriceRefreshTriggerToMaxAgePct"}, {"name": "UpdateAutodeleverageEnabled"}, {"name": "UpdateBorrowingDisabled"}, {"name": "UpdateMinNetValueObligationPostAction"}, {"name": "UpdateMinValueLtvSkipPriorityLiqCheck"}, {"name": "UpdateMinValueBfSkipPriorityLiqCheck"}, {"name": "UpdatePaddingFields"}, {"name": "UpdateName"}, {"name": "UpdateIndividualAutodeleverageMarginCallPeriodSecs"}, {"name": "UpdateInitialDepositAmount"}, {"name": "UpdateObligationOrderExecutionEnabled"}, {"name": "UpdateImmutableFlag"}, {"name": "UpdateObligationOrderCreationEnabled"}]}}, {"name": "UpdateGlobalConfigMode", "type": {"kind": "enum", "variants": [{"name": "PendingAdmin"}, {"name": "FeeCollector"}]}}, {"name": "LastUpdate", "docs": ["Last update state"], "type": {"kind": "struct", "fields": [{"name": "slot", "docs": ["Last slot when updated"], "type": "u64"}, {"name": "stale", "docs": ["True when marked stale, false when slot updated"], "type": "u8"}, {"name": "priceStatus", "docs": ["Status of the prices used to calculate the last update"], "type": "u8"}, {"name": "placeholder", "type": {"array": ["u8", 6]}}]}}, {"name": "ElevationGroup", "type": {"kind": "struct", "fields": [{"name": "maxLiquidationBonusBps", "type": "u16"}, {"name": "id", "type": "u8"}, {"name": "ltvPct", "type": "u8"}, {"name": "liquidationThresholdPct", "type": "u8"}, {"name": "allowNewLoans", "type": "u8"}, {"name": "maxReservesAsCollateral", "type": "u8"}, {"name": "padding0", "type": "u8"}, {"name": "debtReserve", "docs": ["Mandatory debt reserve for this elevation group"], "type": "public<PERSON>ey"}, {"name": "padding1", "type": {"array": ["u64", 4]}}]}}, {"name": "InitObligationArgs", "type": {"kind": "struct", "fields": [{"name": "tag", "type": "u8"}, {"name": "id", "type": "u8"}]}}, {"name": "ObligationCollateral", "docs": ["Obligation collateral state"], "type": {"kind": "struct", "fields": [{"name": "depositReserve", "docs": ["Reserve collateral is deposited to"], "type": "public<PERSON>ey"}, {"name": "depositedAmount", "docs": ["Amount of collateral deposited"], "type": "u64"}, {"name": "marketValueSf", "docs": ["Collateral market value in quote currency (scaled fraction)"], "type": "u128"}, {"name": "borrowedAmountAgainstThisCollateralInElevationGroup", "docs": ["Debt amount (lamport) taken against this collateral.", "(only meaningful if this obligation is part of an elevation group, otherwise 0)", "This is only indicative of the debt computed on the last refresh obligation.", "If the obligation have multiple collateral this value is the same for all of them."], "type": "u64"}, {"name": "padding", "type": {"array": ["u64", 9]}}]}}, {"name": "ObligationLiquidity", "docs": ["Obligation liquidity state"], "type": {"kind": "struct", "fields": [{"name": "borrowReserve", "docs": ["Reserve liquidity is borrowed from"], "type": "public<PERSON>ey"}, {"name": "cumulativeBorrowRateBsf", "docs": ["Borrow rate used for calculating interest (big scaled fraction)"], "type": {"defined": "BigFractionBytes"}}, {"name": "padding", "type": "u64"}, {"name": "borrowedAmountSf", "docs": ["Amount of liquidity borrowed plus interest (scaled fraction)"], "type": "u128"}, {"name": "marketValueSf", "docs": ["Liquidity market value in quote currency (scaled fraction)"], "type": "u128"}, {"name": "borrowFactorAdjustedMarketValueSf", "docs": ["Risk adjusted liquidity market value in quote currency - DEBUG ONLY - use market_value instead"], "type": "u128"}, {"name": "borrowedAmountOutsideElevationGroups", "docs": ["Amount of liquidity borrowed outside of an elevation group"], "type": "u64"}, {"name": "padding2", "type": {"array": ["u64", 7]}}]}}, {"name": "ObligationOrder", "docs": ["A single obligation order.", "See [Obligation::orders]."], "type": {"kind": "struct", "fields": [{"name": "conditionThresholdSf", "docs": ["A threshold value used by the condition (scaled [Fraction]).", "The exact meaning depends on the specific [Self::condition_type].", "", "Examples:", "- when `condition_type == 2 (UserLtvBelow)`:", "then a value of `0.455` here means that the order is active only when the obligation's", "user LTV is less than `0.455` (i.e. < 45.5%).", "- when `condition_type == 3 (DebtCollPriceRatioAbove)`:", "assuming the obligation uses BTC collateral for SOL debt, then a value of `491.3` here", "means that the order is active only when the BTC-SOL price is greater than `491.3` (i.e.", "> 491.3 SOL per BTC)."], "type": "u128"}, {"name": "opportunityParameterSf", "docs": ["A configuration parameter used by the opportunity (scaled [Fraction]).", "The exact meaning depends on the specific [Self::opportunity_type].", "", "Examples:", "- when `opportunity_type == 0 (DeleverageSingleDebtAmount)`:", "Assuming the obligation uses BTC collateral for SOL debt, then a value of `1_234_000_000`", "here means that a liquidator may repay up to 1234000000 lamports (i.e. 1.234 SOL) on this", "obligation.", "Note: the special value of [Fraction::MAX] is *not* allowed in this case.", "- when `opportunity_type == 1 (DeleverageAllDebtAmount)`:", "The only allowed value in this case is [Fraction::MAX] (to emphasize that *all* debt", "should be repaid)."], "type": "u128"}, {"name": "minExecutionBonusBps", "docs": ["A *minimum* additional fraction of collateral transferred to the liquidator, in bps.", "", "The minimum bonus is applied exactly when the [Self::condition_threshold_sf] is met, and", "grows linearly towards the [Self::max_execution_bonus_bps].", "", "Example: a value of `50` here means 50bps == 0.5% bonus for an \"LTV > 65%\" order, when", "executed precisely at the moment LTV exceeds 65%."], "type": "u16"}, {"name": "maxExecutionBonusBps", "docs": ["A *maximum* additional fraction of collateral transferred to the liquidator, in bps.", "", "The maximum bonus is applied at the relevant \"extreme\" state of the obligation, i.e.:", "- for a stop-loss condition, it is a point at which the obligation becomes liquidatable;", "- for a take-profit condition, it is a point at which obligation has 0% LTV.", "", "In non-extreme states, the actual bonus value is interpolated linearly, starting from", "[Self::min_execution_bonus_bps] (at the point specified by the order's condition).", "", "Example: a value of `300` here means 300bps == 3.0% bonus for a \"debt/coll price > 140\"", "order, when executed at a higher price = 200, at which the obligation's LTV happens to", "be equal to its liquidation LTV."], "type": "u16"}, {"name": "conditionType", "docs": ["Serialized [ConditionType].", "The entire order is void when this is zeroed (i.e. representing [ConditionType::Never]).", "", "Example: a value of `2` here denotes `UserLtvBelow` condition type. Of course, to", "interpret this condition, we also need to take the [Self::condition_threshold_sf] into", "account."], "type": "u8"}, {"name": "opportunityType", "docs": ["Serialized [OpportunityType].", "", "Example: a value of `0` here denotes `DeleverageSingleDebtAmount` opportunity. Of course, to", "interpret this opportunity, we also need to take the [Self::opportunity_parameter_sf] into", "account."], "type": "u8"}, {"name": "padding1", "docs": ["Internal padding.", "The fields above take up 2+2+1+1 bytes = 48 bits, which means we need 80 bits = 10 bytes to", "align with `u128`s."], "type": {"array": ["u8", 10]}}, {"name": "padding2", "docs": ["End padding.", "The total size of a single instance is 8*u128 = 128 bytes."], "type": {"array": ["u128", 5]}}]}}, {"name": "AssetTier", "type": {"kind": "enum", "variants": [{"name": "Regular"}, {"name": "IsolatedCollateral"}, {"name": "IsolatedDebt"}]}}, {"name": "BigFractionBytes", "type": {"kind": "struct", "fields": [{"name": "value", "type": {"array": ["u64", 4]}}, {"name": "padding", "type": {"array": ["u64", 2]}}]}}, {"name": "FeeCalculation", "docs": ["Calculate fees exlusive or inclusive of an amount"], "type": {"kind": "enum", "variants": [{"name": "Exclusive"}, {"name": "Inclusive"}]}}, {"name": "ReserveCollateral", "docs": ["Reserve collateral"], "type": {"kind": "struct", "fields": [{"name": "mintPubkey", "docs": ["Reserve collateral mint address"], "type": "public<PERSON>ey"}, {"name": "mintTotalSupply", "docs": ["Reserve collateral mint supply, used for exchange rate"], "type": "u64"}, {"name": "supplyVault", "docs": ["Reserve collateral supply address"], "type": "public<PERSON>ey"}, {"name": "padding1", "type": {"array": ["u128", 32]}}, {"name": "padding2", "type": {"array": ["u128", 32]}}]}}, {"name": "ReserveConfig", "docs": ["Reserve configuration values"], "type": {"kind": "struct", "fields": [{"name": "status", "docs": ["Status of the reserve Active/Obsolete/Hidden"], "type": "u8"}, {"name": "assetTier", "docs": ["Asset tier -> 0 - regular (collateral & debt), 1 - isolated collateral, 2 - isolated debt"], "type": "u8"}, {"name": "hostFixedInterestRateBps", "docs": ["Flat rate that goes to the host"], "type": "u16"}, {"name": "reserved2", "docs": ["[DEPRECATED] Space that used to hold 2 fields:", "- Boost for side (debt or collateral)", "- Reward points multiplier per obligation type", "Can be re-used after making sure all underlying production account data is zeroed."], "type": {"array": ["u8", 9]}}, {"name": "protocolOrderExecutionFeePct", "docs": ["Cut of the order execution bonus that the protocol receives, as a percentage"], "type": "u8"}, {"name": "protocolTakeRatePct", "docs": ["Protocol take rate is the amount borrowed interest protocol receives, as a percentage"], "type": "u8"}, {"name": "protocolLiquidationFeePct", "docs": ["Cut of the liquidation bonus that the protocol receives, as a percentage"], "type": "u8"}, {"name": "loanToValuePct", "docs": ["Target ratio of the value of borrows to deposits, as a percentage", "0 if use as collateral is disabled"], "type": "u8"}, {"name": "liquidationThresholdPct", "docs": ["Loan to value ratio at which an obligation can be liquidated, as percentage"], "type": "u8"}, {"name": "minLiquidationBonusBps", "docs": ["Minimum bonus a liquidator receives when repaying part of an unhealthy obligation, as bps"], "type": "u16"}, {"name": "maxLiquidationBonusBps", "docs": ["Maximum bonus a liquidator receives when repaying part of an unhealthy obligation, as bps"], "type": "u16"}, {"name": "badDebtLiquidationBonusBps", "docs": ["Bad debt liquidation bonus for an undercollateralized obligation, as bps"], "type": "u16"}, {"name": "deleveragingMarginCallPeriodSecs", "docs": ["Time in seconds that must pass before redemptions are enabled after the deposit limit is", "crossed.", "Only relevant when `autodeleverage_enabled == 1`, and must not be 0 in such case."], "type": "u64"}, {"name": "deleveragingThresholdDecreaseBpsPerDay", "docs": ["The rate at which the deleveraging threshold decreases, in bps per day.", "Only relevant when `autodeleverage_enabled == 1`, and must not be 0 in such case."], "type": "u64"}, {"name": "fees", "docs": ["Program owner fees assessed, separate from gains due to interest accrual"], "type": {"defined": "ReserveFees"}}, {"name": "borrowRateCurve", "docs": ["Borrow rate curve based on utilization"], "type": {"defined": "BorrowRateCurve"}}, {"name": "borrowFactorPct", "docs": ["Borrow factor in percentage - used for risk adjustment"], "type": "u64"}, {"name": "depositLimit", "docs": ["Maximum deposit limit of liquidity in native units, u64::MAX for inf"], "type": "u64"}, {"name": "borrowLimit", "docs": ["Maximum amount borrowed, u64::MAX for inf, 0 to disable borrows (protected deposits)"], "type": "u64"}, {"name": "tokenInfo", "docs": ["Token id from TokenInfos struct"], "type": {"defined": "TokenInfo"}}, {"name": "depositWithdrawalCap", "docs": ["Deposit withdrawal caps - deposit & redeem"], "type": {"defined": "WithdrawalCaps"}}, {"name": "debtWithdrawalCap", "docs": ["Debt withdrawal caps - borrow & repay"], "type": {"defined": "WithdrawalCaps"}}, {"name": "elevationGroups", "type": {"array": ["u8", 20]}}, {"name": "disableUsageAsCollOutsideEmode", "type": "u8"}, {"name": "utilizationLimitBlockBorrowingAbovePct", "docs": ["Utilization (in percentage) above which borrowing is blocked. 0 to disable."], "type": "u8"}, {"name": "autodeleverageEnabled", "docs": ["Whether this reserve should be subject to auto-deleveraging after deposit or borrow limit is", "crossed.", "Besides this flag, the lending market's flag also needs to be enabled (logical `AND`).", "**NOTE:** the manual \"target LTV\" deleveraging (enabled by the risk council for individual", "obligations) is NOT affected by this flag."], "type": "u8"}, {"name": "reserved1", "type": {"array": ["u8", 1]}}, {"name": "borrowLimitOutsideElevationGroup", "docs": ["Maximum amount liquidity of this reserve borrowed outside all elevation groups", "- u64::MAX for inf", "- 0 to disable borrows outside elevation groups"], "type": "u64"}, {"name": "borrowLimitAgainstThisCollateralInElevationGroup", "docs": ["Defines the maximum amount (in lamports of elevation group debt asset)", "that can be borrowed when this reserve is used as collateral.", "- u64::MAX for inf", "- 0 to disable borrows in this elevation group (expected value for the debt asset)"], "type": {"array": ["u64", 32]}}, {"name": "deleveragingBonusIncreaseBpsPerDay", "docs": ["The rate at which the deleveraging-related liquidation bonus increases, in bps per day.", "Only relevant when `autodeleverage_enabled == 1`, and must not be 0 in such case."], "type": "u64"}]}}, {"name": "ReserveFarmKind", "type": {"kind": "enum", "variants": [{"name": "Collateral"}, {"name": "Debt"}]}}, {"name": "ReserveFees", "docs": ["Additional fee information on a reserve", "", "These exist separately from interest accrual fees, and are specifically for the program owner", "and referral fee. The fees are paid out as a percentage of liquidity token amounts during", "repayments and liquidations."], "type": {"kind": "struct", "fields": [{"name": "borrowFeeSf", "docs": ["Fee assessed on `BorrowObligationLiquidity`, as scaled fraction (60 bits fractional part)", "Must be between `0` and `2^60`, such that `2^60 = 1`.  A few examples for", "clarity:", "1% = (1 << 60) / 100 = 11529215046068470", "0.01% (1 basis point) = 115292150460685", "0.00001% (Aave borrow fee) = 115292150461"], "type": "u64"}, {"name": "flashLoanFeeSf", "docs": ["Fee for flash loan, expressed as scaled fraction.", "0.3% (Aave flash loan fee) = 0.003 * 2^60 = 3458764513820541"], "type": "u64"}, {"name": "padding", "docs": ["Used for allignment"], "type": {"array": ["u8", 8]}}]}}, {"name": "ReserveLiquidity", "docs": ["Reserve liquidity"], "type": {"kind": "struct", "fields": [{"name": "mintPubkey", "docs": ["Reserve liquidity mint address"], "type": "public<PERSON>ey"}, {"name": "supplyVault", "docs": ["Reserve liquidity supply address"], "type": "public<PERSON>ey"}, {"name": "feeVault", "docs": ["Reserve liquidity fee collection address"], "type": "public<PERSON>ey"}, {"name": "availableAmount", "docs": ["Reserve liquidity available"], "type": "u64"}, {"name": "borrowedAmountSf", "docs": ["Reserve liquidity borrowed (scaled fraction)"], "type": "u128"}, {"name": "marketPriceSf", "docs": ["Reserve liquidity market price in quote currency (scaled fraction)"], "type": "u128"}, {"name": "marketPriceLastUpdatedTs", "docs": ["Unix timestamp of the market price (from the oracle)"], "type": "u64"}, {"name": "mintDecimals", "docs": ["Reserve liquidity mint decimals"], "type": "u64"}, {"name": "depositLimitCrossedTimestamp", "docs": ["Timestamp when the last refresh reserve detected that the liquidity amount is above the deposit cap. When this threshold is crossed, then redemptions (auto-deleverage) are enabled.", "If the threshold is not crossed, then the timestamp is set to 0"], "type": "u64"}, {"name": "borrowLimitCrossedTimestamp", "docs": ["Timestamp when the last refresh reserve detected that the borrowed amount is above the borrow cap. When this threshold is crossed, then redemptions (auto-deleverage) are enabled.", "If the threshold is not crossed, then the timestamp is set to 0"], "type": "u64"}, {"name": "cumulativeBorrowRateBsf", "docs": ["Reserve liquidity cumulative borrow rate (scaled fraction)"], "type": {"defined": "BigFractionBytes"}}, {"name": "accumulatedProtocolFeesSf", "docs": ["Reserve cumulative protocol fees (scaled fraction)"], "type": "u128"}, {"name": "accumulatedReferrerFeesSf", "docs": ["Reserve cumulative referrer fees (scaled fraction)"], "type": "u128"}, {"name": "pendingReferrerFeesSf", "docs": ["Reserve pending referrer fees, to be claimed in refresh_obligation by referrer or protocol (scaled fraction)"], "type": "u128"}, {"name": "absoluteReferralRateSf", "docs": ["Reserve referrer fee absolute rate calculated at each refresh_reserve operation (scaled fraction)"], "type": "u128"}, {"name": "tokenProgram", "docs": ["Token program of the liquidity mint"], "type": "public<PERSON>ey"}, {"name": "padding2", "type": {"array": ["u64", 51]}}, {"name": "padding3", "type": {"array": ["u128", 32]}}]}}, {"name": "ReserveStatus", "type": {"kind": "enum", "variants": [{"name": "Active"}, {"name": "Obsolete"}, {"name": "Hidden"}]}}, {"name": "WithdrawalCaps", "docs": ["Reserve Withdrawal Caps State"], "type": {"kind": "struct", "fields": [{"name": "configCapacity", "type": "i64"}, {"name": "currentTotal", "type": "i64"}, {"name": "lastIntervalStartTimestamp", "type": "u64"}, {"name": "configIntervalLengthSeconds", "type": "u64"}]}}, {"name": "PriceHeuristic", "type": {"kind": "struct", "fields": [{"name": "lower", "docs": ["Lower value of acceptable price"], "type": "u64"}, {"name": "upper", "docs": ["Upper value of acceptable price"], "type": "u64"}, {"name": "exp", "docs": ["Number of decimals of the previously defined values"], "type": "u64"}]}}, {"name": "PythConfiguration", "type": {"kind": "struct", "fields": [{"name": "price", "docs": ["Pubkey of the base price feed (disabled if `null` or `default`)"], "type": "public<PERSON>ey"}]}}, {"name": "ScopeConfiguration", "type": {"kind": "struct", "fields": [{"name": "priceFeed", "docs": ["Pubkey of the scope price feed (disabled if `null` or `default`)"], "type": "public<PERSON>ey"}, {"name": "priceChain", "docs": ["This is the scope_id price chain that results in a price for the token"], "type": {"array": ["u16", 4]}}, {"name": "twa<PERSON><PERSON><PERSON><PERSON>", "docs": ["This is the scope_id price chain for the twap"], "type": {"array": ["u16", 4]}}]}}, {"name": "SwitchboardConfiguration", "type": {"kind": "struct", "fields": [{"name": "priceAggregator", "docs": ["Pubkey of the base price feed (disabled if `null` or `default`)"], "type": "public<PERSON>ey"}, {"name": "twapAggregator", "type": "public<PERSON>ey"}]}}, {"name": "TokenInfo", "type": {"kind": "struct", "fields": [{"name": "name", "docs": ["UTF-8 encoded name of the token (null-terminated)"], "type": {"array": ["u8", 32]}}, {"name": "heuristic", "docs": ["Heuristics limits of acceptable price"], "type": {"defined": "PriceHeuristic"}}, {"name": "maxTwapDivergenceBps", "docs": ["Max divergence between twap and price in bps"], "type": "u64"}, {"name": "maxAgePriceSeconds", "type": "u64"}, {"name": "maxAgeTwapSeconds", "type": "u64"}, {"name": "scopeConfiguration", "docs": ["Scope price configuration"], "type": {"defined": "ScopeConfiguration"}}, {"name": "switchboardConfiguration", "docs": ["Switchboard configuration"], "type": {"defined": "SwitchboardConfiguration"}}, {"name": "pythConfiguration", "docs": ["Pyth configuration"], "type": {"defined": "PythConfiguration"}}, {"name": "blockPriceUsage", "type": "u8"}, {"name": "reserved", "type": {"array": ["u8", 7]}}, {"name": "padding", "type": {"array": ["u64", 19]}}]}}, {"name": "BorrowRateCurve", "type": {"kind": "struct", "fields": [{"name": "points", "type": {"array": [{"defined": "CurvePoint"}, 11]}}]}}, {"name": "CurvePoint", "type": {"kind": "struct", "fields": [{"name": "utilizationRateBps", "type": "u32"}, {"name": "borrowRateBps", "type": "u32"}]}}], "errors": [{"code": 6000, "name": "InvalidMarketAuthority", "msg": "Market authority is invalid"}, {"code": 6001, "name": "InvalidMarketOwner", "msg": "Market owner is invalid"}, {"code": 6002, "name": "InvalidAccountOwner", "msg": "Input account owner is not the program address"}, {"code": 6003, "name": "InvalidAmount", "msg": "Input amount is invalid"}, {"code": 6004, "name": "InvalidConfig", "msg": "Input config value is invalid"}, {"code": 6005, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "msg": "Input account must be a signer"}, {"code": 6006, "name": "InvalidAccountInput", "msg": "Invalid account input"}, {"code": 6007, "name": "MathOverflow", "msg": "Math operation overflow"}, {"code": 6008, "name": "InsufficientLiquidity", "msg": "Insufficient liquidity available"}, {"code": 6009, "name": "ReserveStale", "msg": "Reserve state needs to be refreshed"}, {"code": 6010, "name": "WithdrawTooSmall", "msg": "Withdraw amount too small"}, {"code": 6011, "name": "WithdrawTooLarge", "msg": "Withdraw amount too large"}, {"code": 6012, "name": "BorrowTooSmall", "msg": "Borrow amount too small to receive liquidity after fees"}, {"code": 6013, "name": "BorrowTooLarge", "msg": "Borrow amount too large for deposited collateral"}, {"code": 6014, "name": "RepayTooSmall", "msg": "Repay amount too small to transfer liquidity"}, {"code": 6015, "name": "LiquidationTooSmall", "msg": "Liquidation amount too small to receive collateral"}, {"code": 6016, "name": "ObligationHealthy", "msg": "Cannot liquidate healthy obligations"}, {"code": 6017, "name": "ObligationStale", "msg": "Obligation state needs to be refreshed"}, {"code": 6018, "name": "ObligationReserveLimit", "msg": "Obligation reserve limit exceeded"}, {"code": 6019, "name": "InvalidObligationOwner", "msg": "Obligation owner is invalid"}, {"code": 6020, "name": "ObligationDepositsEmpty", "msg": "Obligation deposits are empty"}, {"code": 6021, "name": "ObligationBorrowsEmpty", "msg": "Obligation borrows are empty"}, {"code": 6022, "name": "ObligationDepositsZero", "msg": "Obligation deposits have zero value"}, {"code": 6023, "name": "ObligationBorrowsZero", "msg": "Obligation borrows have zero value"}, {"code": 6024, "name": "InvalidObligationCollateral", "msg": "Invalid obligation collateral"}, {"code": 6025, "name": "InvalidObligationLiquidity", "msg": "Invalid obligation liquidity"}, {"code": 6026, "name": "ObligationCollateralEmpty", "msg": "Obligation collateral is empty"}, {"code": 6027, "name": "ObligationLiquidityEmpty", "msg": "Obligation liquidity is empty"}, {"code": 6028, "name": "NegativeInterestRate", "msg": "Interest rate is negative"}, {"code": 6029, "name": "InvalidOracleConfig", "msg": "Input oracle config is invalid"}, {"code": 6030, "name": "InsufficientProtocolFeesToRedeem", "msg": "Insufficient protocol fees to claim or no liquidity available"}, {"code": 6031, "name": "FlashBorrowCpi", "msg": "No cpi flash borrows allowed"}, {"code": 6032, "name": "NoFlashRepayFound", "msg": "No corresponding repay found for flash borrow"}, {"code": 6033, "name": "InvalidFlashRepay", "msg": "Invalid repay found"}, {"code": 6034, "name": "FlashRepayCpi", "msg": "No cpi flash repays allowed"}, {"code": 6035, "name": "MultipleFlashBorrows", "msg": "Multiple flash borrows not allowed in the same transaction"}, {"code": 6036, "name": "FlashLoansDisabled", "msg": "Flash loans are disabled for this reserve"}, {"code": 6037, "name": "SwitchboardV2Error", "msg": "Switchboard error"}, {"code": 6038, "name": "CouldNotDeserializeScope", "msg": "Cannot deserialize the scope price account"}, {"code": 6039, "name": "PriceTooOld", "msg": "Price too old"}, {"code": 6040, "name": "PriceTooDivergentFromTwap", "msg": "Price too divergent from twap"}, {"code": 6041, "name": "InvalidTwapPrice", "msg": "Invalid twap price"}, {"code": 6042, "name": "GlobalEmergencyMode", "msg": "Emergency mode is enabled"}, {"code": 6043, "name": "InvalidFlag", "msg": "Invalid lending market config"}, {"code": 6044, "name": "PriceNotValid", "msg": "Price is not valid"}, {"code": 6045, "name": "PriceIsBiggerThanHeuristic", "msg": "Price is bigger than allowed by heuristic"}, {"code": 6046, "name": "PriceIsLowerThanHeuristic", "msg": "Price lower than allowed by heuristic"}, {"code": 6047, "name": "PriceIsZero", "msg": "Price is zero"}, {"code": 6048, "name": "PriceConfidenceTooWide", "msg": "Price confidence too wide"}, {"code": 6049, "name": "IntegerOverflow", "msg": "Conversion between integers failed"}, {"code": 6050, "name": "NoFarmForReserve", "msg": "This reserve does not have a farm"}, {"code": 6051, "name": "IncorrectInstructionInPosition", "msg": "Wrong instruction at expected position"}, {"code": 6052, "name": "NoPriceFound", "msg": "No price found"}, {"code": 6053, "name": "InvalidTwapConfig", "msg": "Invalid Twap configuration: Twap is enabled but one of the enabled price doesn't have a twap"}, {"code": 6054, "name": "InvalidPythPriceAccount", "msg": "Pyth price account does not match configuration"}, {"code": 6055, "name": "InvalidSwitchboardAccount", "msg": "Switchboard account(s) do not match configuration"}, {"code": 6056, "name": "InvalidScopePriceAccount", "msg": "Scope price account does not match configuration"}, {"code": 6057, "name": "ObligationCollateralLtvZero", "msg": "The obligation has one collateral with an LTV set to 0. Withdraw it before withdrawing other collaterals"}, {"code": 6058, "name": "InvalidObligationSeedsValue", "msg": "Seeds must be default pubkeys for tag 0, and mint addresses for tag 1 or 2"}, {"code": 6059, "name": "DeprecatedInvalidObligationId", "msg": "[DEPRECATED] Obligation id must be 0"}, {"code": 6060, "name": "InvalidBorrowRateCurvePoint", "msg": "Invalid borrow rate curve point"}, {"code": 6061, "name": "InvalidUtilizationRate", "msg": "Invalid utilization rate"}, {"code": 6062, "name": "CannotSocializeObligationWithCollateral", "msg": "Obligation hasn't been fully liquidated and debt cannot be socialized."}, {"code": 6063, "name": "ObligationEmpty", "msg": "Obligation has no borrows or deposits."}, {"code": 6064, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>Reached", "msg": "Withdrawal cap is reached"}, {"code": 6065, "name": "LastTimestampGreaterThanCurrent", "msg": "The last interval start timestamp is greater than the current timestamp"}, {"code": 6066, "name": "LiquidationRewardTooSmall", "msg": "The reward amount is less than the minimum acceptable received liquidity"}, {"code": 6067, "name": "IsolatedAssetTierViolation", "msg": "Isolated Asset Tier Violation"}, {"code": 6068, "name": "InconsistentElevationGroup", "msg": "The obligation's elevation group and the reserve's are not the same"}, {"code": 6069, "name": "InvalidElevationGroup", "msg": "The elevation group chosen for the reserve does not exist in the lending market"}, {"code": 6070, "name": "InvalidElevationGroupConfig", "msg": "The elevation group updated has wrong parameters set"}, {"code": 6071, "name": "UnhealthyElevationGroupLtv", "msg": "The current obligation must have most or all its debt repaid before changing the elevation group"}, {"code": 6072, "name": "ElevationGroupNewLoansDisabled", "msg": "Elevation group does not accept any new loans or any new borrows/withdrawals"}, {"code": 6073, "name": "ReserveDeprecated", "msg": "Reserve was deprecated, no longer usable"}, {"code": 6074, "name": "ReferrerAccountNotInitialized", "msg": "Referrer account not initialized"}, {"code": 6075, "name": "ReferrerAccountMintMissmatch", "msg": "Referrer account mint does not match the operation reserve mint"}, {"code": 6076, "name": "ReferrerAccountWrongAddress", "msg": "Referrer account address is not a valid program address"}, {"code": 6077, "name": "ReferrerAccountReferrerMissmatch", "msg": "Referrer account referrer does not match the owner referrer"}, {"code": 6078, "name": "ReferrerAccountMissing", "msg": "Referrer account missing for obligation with referrer"}, {"code": 6079, "name": "InsufficientReferralFeesToRedeem", "msg": "Insufficient referral fees to claim or no liquidity available"}, {"code": 6080, "name": "CpiDisabled", "msg": "CPI disabled for this instruction"}, {"code": 6081, "name": "ShortUrlNotAsciiAlphanumeric", "msg": "Referrer short_url is not ascii alphanumeric"}, {"code": 6082, "name": "ReserveObsolete", "msg": "Reserve is marked as obsolete"}, {"code": 6083, "name": "ElevationGroupAlreadyActivated", "msg": "Obligation already part of the same elevation group"}, {"code": 6084, "name": "ObligationInObsoleteReserve", "msg": "Obligation has a deposit or borrow in an obsolete reserve"}, {"code": 6085, "name": "ReferrerStateOwnerMismatch", "msg": "Referrer state owner does not match the given signer"}, {"code": 6086, "name": "UserMetadataOwnerAlreadySet", "msg": "User metadata owner is already set"}, {"code": 6087, "name": "CollateralNonLiquidatable", "msg": "This collateral cannot be liquidated (LTV set to 0)"}, {"code": 6088, "name": "BorrowingDisabled", "msg": "Borrowing is disabled"}, {"code": 6089, "name": "BorrowLimitExceeded", "msg": "Cannot borrow above borrow limit"}, {"code": 6090, "name": "DepositLimitExceeded", "msg": "Cannot deposit above deposit limit"}, {"code": 6091, "name": "BorrowingDisabledOutsideElevationGroup", "msg": "Reserve does not accept any new borrows outside elevation group"}, {"code": 6092, "name": "NetValueRemainingTooSmall", "msg": "Net value remaining too small"}, {"code": 6093, "name": "WorseLtvBlocked", "msg": "Cannot get the obligation in a worse position"}, {"code": 6094, "name": "LiabilitiesBiggerThanAssets", "msg": "Cannot have more liabilities than assets in a position"}, {"code": 6095, "name": "ReserveTokenBalanceMismatch", "msg": "Reserve state and token account cannot drift"}, {"code": 6096, "name": "ReserveVaultBalanceMismatch", "msg": "Reserve token account has been unexpectedly modified"}, {"code": 6097, "name": "ReserveAccountingMismatch", "msg": "Reserve internal state accounting has been unexpectedly modified"}, {"code": 6098, "name": "BorrowingAboveUtilizationRateDisabled", "msg": "Borrowing above set utilization rate is disabled"}, {"code": 6099, "name": "LiquidationBorrowFactorPriority", "msg": "Liquidation must prioritize the debt with the highest borrow factor"}, {"code": 6100, "name": "LiquidationLowestLiquidationLtvPriority", "msg": "Liquidation must prioritize the collateral with the lowest liquidation LTV"}, {"code": 6101, "name": "ElevationGroupBorrowLimitExceeded", "msg": "Elevation group borrow limit exceeded"}, {"code": 6102, "name": "ElevationGroupWithoutDebtReserve", "msg": "The elevation group does not have a debt reserve defined"}, {"code": 6103, "name": "ElevationGroupMaxCollateralReserveZero", "msg": "The elevation group does not allow any collateral reserves"}, {"code": 6104, "name": "ElevationGroupHasAnotherDebtReserve", "msg": "In elevation group attempt to borrow from a reserve that is not the debt reserve"}, {"code": 6105, "name": "ElevationGroupDebtReserveAsCollateral", "msg": "The elevation group's debt reserve cannot be used as a collateral reserve"}, {"code": 6106, "name": "ObligationCollateralExceedsElevationGroupLimit", "msg": "Obligation have more collateral than the maximum allowed by the elevation group"}, {"code": 6107, "name": "ObligationElevationGroupMultipleDebtReserve", "msg": "Obligation is an elevation group but have more than one debt reserve"}, {"code": 6108, "name": "UnsupportedTokenExtension", "msg": "Mint has a token (2022) extension that is not supported"}, {"code": 6109, "name": "InvalidTokenAccount", "msg": "Can't have an spl token mint with a t22 account"}, {"code": 6110, "name": "DepositDisabledOutsideElevationGroup", "msg": "Can't deposit into this reserve outside elevation group"}, {"code": 6111, "name": "CannotCalculateReferralAmountDueToSlotsMismatch", "msg": "Cannot calculate referral amount due to slots mismatch"}, {"code": 6112, "name": "ObligationOwnersMustMatch", "msg": "Obligation owners must match"}, {"code": 6113, "name": "ObligationsMustMatch", "msg": "Obligations must match"}, {"code": 6114, "name": "LendingMarketsMustMatch", "msg": "Lending markets must match"}, {"code": 6115, "name": "ObligationCurrentlyMarkedForDeleveraging", "msg": "Obligation is already marked for deleveraging"}, {"code": 6116, "name": "MaximumWithdrawValueZero", "msg": "Maximum withdrawable value of this collateral is zero, LTV needs improved"}, {"code": 6117, "name": "ZeroMaxLtvAssetsInDeposits", "msg": "No max LTV 0 assets allowed in deposits for repay and withdraw"}, {"code": 6118, "name": "LowestLtvAssetsPriority", "msg": "Withdrawing must prioritize the collateral with the lowest reserve max-LTV"}, {"code": 6119, "name": "WorseLtvThanUnhealthyLtv", "msg": "Cannot get the obligation liquidatable"}, {"code": 6120, "name": "FarmAccountsMissing", "msg": "Farm accounts to refresh are missing"}, {"code": 6121, "name": "RepayTooSmallForFullLiquidation", "msg": "Repay amount is too small to satisfy the mandatory full liquidation"}, {"code": 6122, "name": "InsufficientRepayAmount", "msg": "Liquidator provided repay amount lower than required by liquidation rules"}, {"code": 6123, "name": "OrderIndexOutOfBounds", "msg": "Obligation order of the given index cannot exist"}, {"code": 6124, "name": "InvalidOrderConfiguration", "msg": "Given order configuration has wrong parameters"}, {"code": 6125, "name": "OrderConfigurationNotSupportedByObligation", "msg": "Given order configuration cannot be used with the current state of the obligation"}, {"code": 6126, "name": "OperationNotPermittedWithCurrentObligationOrders", "msg": "Single debt, single collateral obligation orders have to be cancelled before changing the deposit/borrow count"}, {"code": 6127, "name": "OperationNotPermittedMarketImmutable", "msg": "Cannot update lending market because it is set as immutable"}, {"code": 6128, "name": "OrderCreationDisabled", "msg": "Creation of new orders is disabled"}, {"code": 6129, "name": "NoUpgradeAuthority", "msg": "Cannot initialize global config because there is no upgrade authority to the program"}]}
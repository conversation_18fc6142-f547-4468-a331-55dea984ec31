//! 闪电贷管理器
//!
//! 提供统一的闪电贷接口，支持多个闪电贷提供商

use anchor_lang::prelude::*;
use crate::constants::FlashLoanConfig;
use crate::error::RouteError;
use crate::state::event::{FlashLoanExecuted, SecurityEvent, security_event_types, severity_levels};
use crate::flash_loan::KaminoFlashLoanAdapter;
use crate::RouteConfig;

/// 闪电贷提供商枚举
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Copy, Debug, PartialEq)]
pub enum FlashLoanProvider {
    Kamino = 0,
    Solend = 1,
    Mango = 2,
}

impl Default for FlashLoanProvider {
    fn default() -> Self {
        Self::Kamino
    }
}

/// 闪电贷管理器
pub struct FlashLoanManager;

impl FlashLoanManager {
    /// 创建新的闪电贷管理器
    pub fn new() -> Self {
        Self
    }

    /// 执行闪电贷套利
    pub fn execute_flash_loan_arbitrage<'info>(
        &self,
        flash_loan_config: &FlashLoanConfig,
        _arbitrage_config: &RouteConfig,
        accounts: &'info [AccountInfo<'info>],
        _remaining_accounts: &'info [AccountInfo<'info>],
    ) -> Result<u64> {
        // 验证闪电贷配置
        Self::validate_flash_loan_config(flash_loan_config)?;

        // 安全检查
        Self::perform_security_checks(flash_loan_config)?;

        let _start_time = Clock::get()?.unix_timestamp;

        // 首先执行闪电贷获取资金
        let flash_loan_result = Self::execute_flash_loan(
            flash_loan_config,
            accounts,
            &[],
        );

        let end_time = Clock::get()?.unix_timestamp;

        match flash_loan_result {
            Ok(profit) => {
                // 发出成功事件
                emit!(FlashLoanExecuted {
                    user: Some(flash_loan_config.borrower),
                    provider: flash_loan_config.provider_program,
                    amount: flash_loan_config.amount,
                    fee: Self::calculate_flash_loan_fee(flash_loan_config)?,
                    success: true,
                    profit: profit as i64,
                    timestamp: end_time,
                });
                Ok(profit)
            },
            Err(e) => {
                // 发出失败事件
                emit!(FlashLoanExecuted {
                    user: Some(flash_loan_config.borrower),
                    provider: flash_loan_config.provider_program,
                    amount: flash_loan_config.amount,
                    fee: 0,
                    success: false,
                    profit: 0,
                    timestamp: end_time,
                });
                Err(e)
            }
        }
    }

    /// 执行闪电贷
    pub fn execute_flash_loan<'info>(
        config: &FlashLoanConfig,
        accounts: &'info [AccountInfo<'info>],
        callback_data: &[u8],
    ) -> Result<u64> {
        // 验证闪电贷配置
        Self::validate_flash_loan_config(config)?;

        // 安全检查
        Self::perform_security_checks(config)?;

        let _start_time = Clock::get()?.unix_timestamp;

        // 根据提供商选择适配器
        let result = match config.provider {
            0 => { // Kamino
                KaminoFlashLoanAdapter::execute_flash_loan(
                    config,
                    accounts,
                    callback_data,
                )
            },
            1 => { // Solend
                // TODO: 实现 Solend 适配器
                Err(RouteError::UnsupportedFlashLoanProvider.into())
            },
            2 => { // Mango
                // TODO: 实现 Mango 适配器
                Err(RouteError::UnsupportedFlashLoanProvider.into())
            },
            _ => Err(RouteError::UnsupportedFlashLoanProvider.into()),
        };

        let end_time = Clock::get()?.unix_timestamp;

        // 发出闪电贷执行事件
        match &result {
            Ok(profit) => {
                emit!(FlashLoanExecuted {
                    user: Some(config.borrower),
                    provider: config.provider_program,
                    amount: config.amount,
                    fee: Self::calculate_flash_loan_fee(config)?,
                    success: true,
                    profit: *profit as i64,
                    timestamp: end_time,
                });
            },
            Err(_) => {
                emit!(FlashLoanExecuted {
                    user: Some(config.borrower),
                    provider: config.provider_program,
                    amount: config.amount,
                    fee: 0,
                    success: false,
                    profit: 0,
                    timestamp: end_time,
                });
            }
        }

        result
    }

    /// 验证闪电贷配置
    fn validate_flash_loan_config(config: &FlashLoanConfig) -> Result<()> {
        require!(
            config.amount > 0,
            RouteError::ZeroAmount
        );

        require!(
            config.amount <= 1_000_000_000_000, // 1M SOL 最大限制
            RouteError::FlashLoanAmountExceeded
        );

        require!(
            config.max_fee_bps <= 1000, // 最大10%费率
            RouteError::ExcessiveFlashLoanFee
        );

        Ok(())
    }

    /// 执行安全检查
    fn perform_security_checks(config: &FlashLoanConfig) -> Result<()> {
        // 检查借贷金额是否异常大
        if config.amount > 100_000_000_000 { // 100K SOL
            emit!(SecurityEvent {
                event_type: security_event_types::SECURITY_ALERT,
                severity: severity_levels::WARNING,
                user: Some(config.borrower),
                dex: None,
                input_mint: None,
                output_mint: None,
                amount: config.amount,
                max_allowed_amount: 100_000_000_000,
                score_or_impact_bps: 0,
                approved: true, // 警告但不阻止
                description: "大额闪电贷请求".to_string(),
                timestamp: Clock::get()?.unix_timestamp,
            });
        }

        // 检查费率设置是否合理
        if config.max_fee_bps > 500 { // 超过5%
            emit!(SecurityEvent {
                event_type: security_event_types::SECURITY_ALERT,
                severity: severity_levels::WARNING,
                user: Some(config.borrower),
                dex: None,
                input_mint: None,
                output_mint: None,
                amount: config.amount,
                max_allowed_amount: config.amount,
                score_or_impact_bps: config.max_fee_bps,
                approved: true,
                description: "高费率闪电贷设置".to_string(),
                timestamp: Clock::get()?.unix_timestamp,
            });
        }

        Ok(())
    }

    /// 计算闪电贷费用
    pub fn calculate_flash_loan_fee(config: &FlashLoanConfig) -> Result<u64> {
        let fee_rate = match config.provider {
            0 => 5, // Kamino: 0.05% = 5 bps
            1 => 3, // Solend: 0.03% = 3 bps
            2 => 2, // Mango: 0.02% = 2 bps
            _ => 5, // 默认使用 Kamino 费率
        };

        let fee = config.amount
            .checked_mul(fee_rate)
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        // 确保费用不超过用户设置的最大值
        let max_fee = config.amount
            .checked_mul(config.max_fee_bps as u64)
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        require!(
            fee <= max_fee,
            RouteError::ExcessiveFlashLoanFee
        );

        Ok(fee)
    }

    /// 估算闪电贷可用性
    pub fn estimate_availability(
        provider: u8,
        amount: u64,
    ) -> Result<FlashLoanAvailability> {
        match provider {
            0 => { // Kamino
                KaminoFlashLoanAdapter::check_availability(amount)
            },
            _ => Ok(FlashLoanAvailability {
                available: false,
                max_amount: 0,
                current_utilization_bps: 10000, // 100% - 不可用
                estimated_fee_bps: 0,
            }),
        }
    }

    /// 获取最佳闪电贷提供商
    pub fn get_best_provider(amount: u64) -> Result<(u8, u64)> {
        let providers = [0u8, 1u8, 2u8]; // Kamino, Solend, Mango

        let mut best_provider = 0u8; // 默认 Kamino
        let mut lowest_fee = u64::MAX;

        for &provider in &providers {
            let availability = Self::estimate_availability(provider, amount)?;

            if availability.available {
                let estimated_fee = amount
                    .checked_mul(availability.estimated_fee_bps as u64)
                    .and_then(|x| x.checked_div(10000))
                    .unwrap_or(u64::MAX);

                if estimated_fee < lowest_fee {
                    lowest_fee = estimated_fee;
                    best_provider = provider;
                }
            }
        }

        require!(
            lowest_fee != u64::MAX,
            RouteError::NoFlashLoanProvider
        );

        Ok((best_provider, lowest_fee))
    }

    /// 预执行检查
    pub fn pre_execution_check(config: &FlashLoanConfig) -> Result<PreExecutionStatus> {
        // 验证配置
        Self::validate_flash_loan_config(config)?;

        // 检查可用性
        let availability = Self::estimate_availability(config.provider, config.amount)?;

        if !availability.available {
            return Ok(PreExecutionStatus {
                can_proceed: false,
                estimated_fee: 0,
                risk_score: 100,
                warnings: vec!["闪电贷不可用".to_string()],
                max_recommended_amount: availability.max_amount,
            });
        }

        // 计算风险评分
        let risk_score = Self::calculate_risk_score(config, &availability)?;

        // 生成警告
        let mut warnings = Vec::new();
        if config.amount > availability.max_amount / 2 {
            warnings.push("借贷金额较大，可能影响池子利用率".to_string());
        }

        if risk_score > 70 {
            warnings.push("高风险闪电贷操作".to_string());
        }

        let estimated_fee = Self::calculate_flash_loan_fee(config)?;

        Ok(PreExecutionStatus {
            can_proceed: risk_score <= 85,
            estimated_fee,
            risk_score,
            warnings,
            max_recommended_amount: availability.max_amount,
        })
    }

    /// 计算风险评分 (0-100)
    fn calculate_risk_score(
        config: &FlashLoanConfig,
        availability: &FlashLoanAvailability,
    ) -> Result<u8> {
        let mut score = 0u8;

        // 基于金额大小的风险 (0-30分)
        let amount_ratio = (config.amount * 10000 / availability.max_amount.max(1)) as u16;
        score += ((amount_ratio / 100).min(30)) as u8;

        // 基于池子利用率的风险 (0-25分)
        score += (availability.current_utilization_bps / 400).min(25) as u8;

        // 基于费率设置的风险 (0-20分)
        if config.max_fee_bps > 300 { // 超过3%
            score += 10;
        }
        if config.max_fee_bps > 500 { // 超过5%
            score += 10;
        }

        // 基于提供商可靠性的风险 (0-25分)
        let provider_risk = match config.provider {
            0 => 5,  // Kamino - 低风险
            1 => 10, // Solend - 中风险
            2 => 15, // Mango - 高风险
            _ => 15, // 未知提供商 - 高风险
        };
        score += provider_risk;

        Ok(score.min(100))
    }
}

/// 闪电贷可用性信息
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct FlashLoanAvailability {
    /// 是否可用
    pub available: bool,
    /// 最大可借贷金额
    pub max_amount: u64,
    /// 当前利用率 (基点)
    pub current_utilization_bps: u16,
    /// 预估费率 (基点)
    pub estimated_fee_bps: u16,
}

/// 预执行状态
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PreExecutionStatus {
    /// 是否可以继续执行
    pub can_proceed: bool,
    /// 预估费用
    pub estimated_fee: u64,
    /// 风险评分 (0-100)
    pub risk_score: u8,
    /// 警告信息
    pub warnings: Vec<String>,
    /// 最大建议金额
    pub max_recommended_amount: u64,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validate_flash_loan_config() {
        let valid_config = FlashLoanConfig {
            provider: 0, // Kamino
            provider_program: Pubkey::new_unique(),
            borrower: Pubkey::new_unique(),
            amount: 1000000,
            max_fee_bps: 100,
        };

        assert!(FlashLoanManager::validate_flash_loan_config(&valid_config).is_ok());

        // 测试零金额
        let mut invalid_config = valid_config.clone();
        invalid_config.amount = 0;
        assert!(FlashLoanManager::validate_flash_loan_config(&invalid_config).is_err());

        // 测试过高费率
        let mut invalid_config = valid_config.clone();
        invalid_config.max_fee_bps = 1001;
        assert!(FlashLoanManager::validate_flash_loan_config(&invalid_config).is_err());
    }

    #[test]
    fn test_calculate_flash_loan_fee() {
        let config = FlashLoanConfig {
            provider: 0, // Kamino
            provider_program: Pubkey::new_unique(),
            borrower: Pubkey::new_unique(),
            amount: 1000000,
            max_fee_bps: 100, // 1%
        };

        let fee = FlashLoanManager::calculate_flash_loan_fee(&config);
        assert!(fee.is_ok());

        // Kamino 费率为 5 bps = 0.05%
        let expected_fee = 1000000 * 5 / 10000; // 500
        assert_eq!(fee.unwrap(), expected_fee);
    }

    #[test]
    fn test_calculate_risk_score() {
        let config = FlashLoanConfig {
            provider: 0, // Kamino
            provider_program: Pubkey::new_unique(),
            borrower: Pubkey::new_unique(),
            amount: 1000000,
            max_fee_bps: 50,
        };

        let availability = FlashLoanAvailability {
            available: true,
            max_amount: 10000000,
            current_utilization_bps: 5000, // 50%
            estimated_fee_bps: 5,
        };

        let score = FlashLoanManager::calculate_risk_score(&config, &availability);
        assert!(score.is_ok());

        let risk_score = score.unwrap();
        assert!(risk_score <= 100);

        // 低风险场景应该有较低的评分
        assert!(risk_score < 50);
    }
}

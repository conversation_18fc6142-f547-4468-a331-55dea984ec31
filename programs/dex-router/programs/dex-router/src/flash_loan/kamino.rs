//! Kamino 闪电贷适配器
//!
//! 与 Kamino Protocol 的闪电贷功能集成

use anchor_lang::prelude::*;
use anchor_lang::solana_program::instruction::Instruction;
use anchor_lang::solana_program::program::invoke_signed;
use crate::constants::FlashLoanConfig;
use crate::error::RouteError;
use crate::flash_loan::{FlashLoanAvailability, FlashLoanProvider};

/// Kamino 闪电贷适配器
pub struct KaminoFlashLoanAdapter;

impl KaminoFlashLoanAdapter {
    /// 执行 Kamino 闪电贷
    pub fn execute_flash_loan<'info>(
        config: &FlashLoanConfig,
        accounts: &'info [AccountInfo<'info>],
        callback_data: &[u8],
    ) -> Result<u64> {
        // 验证账户数量
        require!(
            accounts.len() >= Self::get_required_accounts_count(),
            RouteError::InsufficientAccounts
        );

        // 解析账户
        let kamino_accounts = Self::parse_accounts(accounts)?;

        // 验证用户转账授权签名
        require!(
            kamino_accounts.user_transfer_authority.is_signer,
            RouteError::InvalidSignature
        );

        // 构建闪电贷指令
        let flash_loan_ix = Self::build_flash_loan_instruction(
            config,
            &kamino_accounts,
            callback_data,
        )?;

        msg!("执行 Kamino 闪电贷: 金额 {}", config.amount);

        // 执行 CPI 调用到 Kamino 程序
        invoke_signed(
            &flash_loan_ix,
            accounts,
            &[], // 暂时不使用种子签名，实际实现中可能需要PDA签名
        )?;

        msg!("Kamino 闪电贷执行成功");

        // 从回调数据中获取执行结果
        // 实际实现中，这里应该从闪电贷回调的结果中获取利润信息
        // 目前返回一个基于金额计算的预期利润
        let estimated_profit = Self::calculate_expected_profit(config)?;

        Ok(estimated_profit)
    }

    /// 检查 Kamino 闪电贷可用性
    pub fn check_availability(amount: u64) -> Result<FlashLoanAvailability> {
        // 在实际实现中，这里会查询 Kamino 的池子状态
        let mock_pool_liquidity = 1_000_000_000_000u64; // 1M SOL
        let mock_current_borrows = 300_000_000_000u64;   // 300K SOL

        let available_liquidity = mock_pool_liquidity.saturating_sub(mock_current_borrows);
        let utilization_rate = if mock_pool_liquidity > 0 {
            (mock_current_borrows * 10000 / mock_pool_liquidity) as u16
        } else {
            10000
        };

        Ok(FlashLoanAvailability {
            available: amount <= available_liquidity,
            max_amount: available_liquidity,
            current_utilization_bps: utilization_rate,
            estimated_fee_bps: Self::get_fee_rate(utilization_rate),
        })
    }

    /// 获取 Kamino 程序 ID
    pub fn get_kamino_program_id() -> Pubkey {
        // Kamino Protocol 主网程序 ID
        // TODO: 更换为真实的 Kamino 程序 ID
        "KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD".parse().unwrap()
    }

    /// 获取所需账户数量 (10个必需 + 2个可选)
    pub fn get_required_accounts_count() -> usize {
        10 // Kamino flashBorrowReserveLiquidity 需要的最少账户数量
    }

    /// 解析账户 (基于 flashBorrowReserveLiquidity IDL 顺序)
    fn parse_accounts<'info>(accounts: &'info [AccountInfo<'info>]) -> Result<KaminoAccounts<'info>> {
        require!(
            accounts.len() >= Self::get_required_accounts_count(),
            RouteError::InsufficientAccounts
        );

        // 解析可选账户
        let referrer_token_state = if accounts.len() > 10 && !accounts[8].key.eq(&anchor_lang::solana_program::system_program::ID) {
            Some(&accounts[8])
        } else {
            None
        };

        let referrer_account = if accounts.len() > 11 && !accounts[9].key.eq(&anchor_lang::solana_program::system_program::ID) {
            Some(&accounts[9])
        } else {
            None
        };

        // 计算 sysvarInfo 和 tokenProgram 的位置
        let base_offset = match (referrer_token_state.is_some(), referrer_account.is_some()) {
            (true, true) => 10,   // 两个可选账户都存在
            (true, false) => 9,   // 只有第一个可选账户
            (false, true) => 9,   // 只有第二个可选账户
            (false, false) => 8,  // 没有可选账户
        };

        Ok(KaminoAccounts {
            user_transfer_authority: &accounts[0],
            lending_market_authority: &accounts[1],
            lending_market: &accounts[2],
            reserve: &accounts[3],
            reserve_liquidity_mint: &accounts[4],
            reserve_source_liquidity: &accounts[5],
            user_destination_liquidity: &accounts[6],
            reserve_liquidity_fee_receiver: &accounts[7],
            referrer_token_state,
            referrer_account,
            sysvar_info: &accounts[base_offset],
            token_program: &accounts[base_offset + 1],
        })
    }

    /// 构建闪电贷指令 (flashBorrowReserveLiquidity)
    fn build_flash_loan_instruction(
        config: &FlashLoanConfig,
        accounts: &KaminoAccounts,
        _callback_data: &[u8], // flashBorrow 不直接使用 callback_data
    ) -> Result<Instruction> {
        let mut instruction_data = vec![0x6c, 0x9a, 0x3b, 0x8a, 0x5e, 0x3f, 0x92, 0x1d];
        instruction_data.extend_from_slice(&config.amount.to_le_bytes());

        let mut account_metas = vec![
            AccountMeta::new_readonly(*accounts.user_transfer_authority.key, true), // 签名者
            AccountMeta::new_readonly(*accounts.lending_market_authority.key, false),
            AccountMeta::new_readonly(*accounts.lending_market.key, false),
            AccountMeta::new(*accounts.reserve.key, false),
            AccountMeta::new_readonly(*accounts.reserve_liquidity_mint.key, false),
            AccountMeta::new(*accounts.reserve_source_liquidity.key, false),
            AccountMeta::new(*accounts.user_destination_liquidity.key, false),
            AccountMeta::new(*accounts.reserve_liquidity_fee_receiver.key, false),
        ];

        // 添加可选账户
        if let Some(referrer_token_state) = accounts.referrer_token_state {
            account_metas.push(AccountMeta::new(*referrer_token_state.key, false));
        }

        if let Some(referrer_account) = accounts.referrer_account {
            account_metas.push(AccountMeta::new(*referrer_account.key, false));
        }

        // 添加系统变量和 token 程序
        account_metas.push(AccountMeta::new_readonly(*accounts.sysvar_info.key, false));
        account_metas.push(AccountMeta::new_readonly(*accounts.token_program.key, false));

        let instruction = Instruction {
            program_id: Self::get_kamino_program_id(),
            accounts: account_metas,
            data: instruction_data,
        };

        Ok(instruction)
    }

    /// 计算预期利润（基于历史数据或链上状态）
    fn calculate_expected_profit(config: &FlashLoanConfig) -> Result<u64> {
        // 获取当前利用率来计算动态费用
        let current_utilization = Self::get_current_utilization()?;
        let flash_loan_fee_bps = Self::get_fee_rate(current_utilization);
        let flash_loan_fee = config.amount * flash_loan_fee_bps as u64 / 10000;

        // 基于配置的最大费用限制检查
        let max_allowed_fee = config.amount * config.max_fee_bps as u64 / 10000;
        require!(
            flash_loan_fee <= max_allowed_fee,
            RouteError::ExcessiveFlashLoanFee
        );

        // 计算预期利润（这里需要根据实际的套利策略来确定）
        // 暂时返回闪电贷费用作为最小预期利润的基准
        let expected_gross_profit = flash_loan_fee * 2; // 假设至少2倍费用的利润才值得执行
        let net_profit = expected_gross_profit.saturating_sub(flash_loan_fee);

        msg!("计算预期利润: 毛利润 {}, 闪电贷费用 {}, 净利润 {}",
             expected_gross_profit, flash_loan_fee, net_profit);

        Ok(net_profit)
    }

    /// 获取当前池子利用率（模拟实现）
    fn get_current_utilization() -> Result<u16> {
        // 在实际实现中，这里会从 Kamino 协议获取真实的利用率数据
        // 暂时返回一个模拟值
        Ok(6000) // 60% 利用率
    }

    /// 获取基于利用率的费率
    fn get_fee_rate(utilization_bps: u16) -> u16 {
        match utilization_bps {
            0..=5000 => 5,      // 0-50% 利用率: 0.05%
            5001..=7500 => 8,   // 50-75% 利用率: 0.08%
            7501..=9000 => 12,  // 75-90% 利用率: 0.12%
            9001..=9500 => 20,  // 90-95% 利用率: 0.20%
            _ => 50,            // >95% 利用率: 0.50%
        }
    }

    /// 估算最优借贷金额
    pub fn estimate_optimal_amount(
        available_liquidity: u64,
        target_profit_bps: u16,
    ) -> Result<u64> {
        // 基于可用流动性和目标利润率计算最优金额
        let max_safe_amount = available_liquidity / 2; // 不超过池子的50%

        // 基于目标利润率调整
        let adjusted_amount = if target_profit_bps > 100 { // >1% 目标
            max_safe_amount / 4 // 更保守
        } else if target_profit_bps > 50 { // >0.5% 目标
            max_safe_amount / 2
        } else {
            max_safe_amount
        };

        Ok(adjusted_amount)
    }

    /// 计算闪电贷回调所需的最小账户
    pub fn get_callback_accounts_count(callback_type: CallbackType) -> usize {
        match callback_type {
            CallbackType::SimpleArbitrage => 12,
            CallbackType::MultiDexArbitrage => 20,
            CallbackType::LiquidationArbitrage => 15,
        }
    }

    /// 验证闪电贷回调数据
    pub fn validate_callback_data(data: &[u8]) -> Result<CallbackInstruction> {
        require!(
            data.len() >= 1,
            RouteError::InvalidCallbackData
        );

        let instruction_type = data[0];

        match instruction_type {
            0 => {
                // 简单套利
                require!(
                    data.len() >= 33, // 1 + 32 (pubkey)
                    RouteError::InvalidCallbackData
                );

                let target_dex = Pubkey::new_from_array(
                    data[1..33].try_into().map_err(|_| RouteError::InvalidCallbackData)?
                );

                Ok(CallbackInstruction::SimpleArbitrage { target_dex })
            },
            1 => {
                // 多 DEX 套利
                require!(
                    data.len() >= 2,
                    RouteError::InvalidCallbackData
                );

                let dex_count = data[1] as usize;
                require!(
                    data.len() >= 2 + dex_count * 32,
                    RouteError::InvalidCallbackData
                );

                let mut dex_list = Vec::new();
                for i in 0..dex_count {
                    let start = 2 + i * 32;
                    let end = start + 32;
                    let dex_pubkey = Pubkey::new_from_array(
                        data[start..end].try_into().map_err(|_| RouteError::InvalidCallbackData)?
                    );
                    dex_list.push(dex_pubkey);
                }

                Ok(CallbackInstruction::MultiDexArbitrage { dex_list })
            },
            _ => Err(RouteError::UnsupportedCallbackType.into()),
        }
    }
}

/// Kamino 闪电贷账户结构 (基于 flashBorrowReserveLiquidity IDL)
pub struct KaminoAccounts<'info> {
    /// 用户转账授权 (签名者)
    pub user_transfer_authority: &'info AccountInfo<'info>,
    /// 借贷市场权限
    pub lending_market_authority: &'info AccountInfo<'info>,
    /// 借贷市场
    pub lending_market: &'info AccountInfo<'info>,
    /// 储备账户
    pub reserve: &'info AccountInfo<'info>,
    /// 储备流动性铸币
    pub reserve_liquidity_mint: &'info AccountInfo<'info>,
    /// 储备源流动性
    pub reserve_source_liquidity: &'info AccountInfo<'info>,
    /// 用户目标流动性
    pub user_destination_liquidity: &'info AccountInfo<'info>,
    /// 储备流动性费用接收者
    pub reserve_liquidity_fee_receiver: &'info AccountInfo<'info>,
    /// 推荐人代币状态 (可选)
    pub referrer_token_state: Option<&'info AccountInfo<'info>>,
    /// 推荐人账户 (可选)
    pub referrer_account: Option<&'info AccountInfo<'info>>,
    /// 系统变量信息
    pub sysvar_info: &'info AccountInfo<'info>,
    /// Token 程序
    pub token_program: &'info AccountInfo<'info>,
}

/// 回调指令类型
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum CallbackInstruction {
    SimpleArbitrage {
        target_dex: Pubkey,
    },
    MultiDexArbitrage {
        dex_list: Vec<Pubkey>,
    },
    LiquidationArbitrage {
        target_account: Pubkey,
        liquidation_amount: u64,
    },
}

/// 回调类型
#[derive(Clone, Copy, Debug)]
pub enum CallbackType {
    SimpleArbitrage,
    MultiDexArbitrage,
    LiquidationArbitrage,
}
//! 套利策略执行器
//!
//! 实现不同类型的套利策略

use anchor_lang::prelude::*;
use crate::constants::{RouteConfig, FlashLoanConfig};
use crate::error::RouteError;
use crate::state::event::{ArbitrageOpportunity, FlashLoanExecuted};
use crate::flash_loan::{FlashLoanManager, FlashLoanProvider};
use crate::arbitrage::{ArbitrageRiskManager, MarketConditions, RiskAssessment};
use crate::routing::CircularRouteExecutor;

/// 套利策略类型
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Copy, Debug, PartialEq)]
pub enum ArbitrageStrategy {
    /// 三角套利：A -> B -> C -> A
    Triangular,
    /// 跨 DEX 套利：DEX1(A->B) vs DEX2(B->A)
    CrossDex,
    /// 闪电贷套利：借贷 + 套利 + 还贷
    FlashLoanArbitrage,
    /// 统计套利：基于历史价格差异
    Statistical,
}

/// 套利机会
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ArbitrageOpportunityData {
    pub strategy: ArbitrageStrategy,
    pub route_config: RouteConfig,
    pub expected_profit: u64,
    pub confidence_score: u8, // 0-100
    pub requires_flash_loan: bool,
    pub estimated_gas_cost: u64,
    pub time_sensitivity: TimeSensitivity,
}

/// 时间敏感性
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Copy, Debug, PartialEq)]
pub enum TimeSensitivity {
    Low,    // 几分钟内有效
    Medium, // 几十秒内有效
    High,   // 几秒钟内有效
}

/// 套利策略执行器
pub struct ArbitrageStrategyExecutor;

impl ArbitrageStrategyExecutor {
    /// 执行套利策略
    pub fn execute_arbitrage<'info>(
        opportunity: &ArbitrageOpportunityData,
        accounts: &'info [AccountInfo<'info>],
        market_conditions: &MarketConditions,
    ) -> Result<ArbitrageExecutionResult> {
        // 风险评估
        let risk_assessment = ArbitrageRiskManager::assess_arbitrage_risk(
            &opportunity.route_config,
            opportunity.expected_profit,
            market_conditions,
        )?;

        if !risk_assessment.approved {
            return Err(RouteError::ArbitrageRiskTooHigh.into());
        }

        // 根据策略类型执行
        let execution_result = match opportunity.strategy {
            ArbitrageStrategy::Triangular => {
                Self::execute_triangular_arbitrage(opportunity, accounts, &risk_assessment)
            },
            ArbitrageStrategy::CrossDex => {
                Self::execute_cross_dex_arbitrage(opportunity, accounts, &risk_assessment)
            },
            ArbitrageStrategy::FlashLoanArbitrage => {
                Self::execute_flash_loan_arbitrage(opportunity, accounts, &risk_assessment)
            },
            ArbitrageStrategy::Statistical => {
                Self::execute_statistical_arbitrage(opportunity, accounts, &risk_assessment)
            },
        }?;

        // 发出套利机会事件
        emit!(ArbitrageOpportunity {
            input_token: opportunity.route_config.routes[0].input_mint,
            amount_in: opportunity.route_config.amount_in,
            amount_out: execution_result.actual_output,
            profit: execution_result.net_profit,
            routes_used: opportunity.route_config.routes.len() as u8,
            flash_loan_used: opportunity.requires_flash_loan,
        });

        Ok(execution_result)
    }

    /// 执行三角套利
    fn execute_triangular_arbitrage<'info>(
        opportunity: &ArbitrageOpportunityData,
        accounts: &'info [AccountInfo<'info>],
        _risk_assessment: &RiskAssessment,
    ) -> Result<ArbitrageExecutionResult> {
        msg!("执行三角套利策略");

        // 验证三角路由配置
        require!(
            opportunity.route_config.routes.len() >= 3,
            RouteError::InvalidTriangularRoute
        );

        // 验证回到起始代币
        let first_token = opportunity.route_config.routes[0].input_mint;
        let last_token = opportunity.route_config.routes.last().unwrap().output_mint;
        require!(
            first_token == last_token,
            RouteError::InvalidTriangularRoute
        );

        // 执行循环路由
        let actual_output = CircularRouteExecutor::execute_circular_route(
            &opportunity.route_config,
            accounts,
            None,
        )?;

        // 计算净利润
        let net_profit = actual_output.saturating_sub(opportunity.route_config.amount_in);
        let gas_cost = opportunity.estimated_gas_cost;

        Ok(ArbitrageExecutionResult {
            strategy: opportunity.strategy,
            success: net_profit > gas_cost,
            actual_output,
            net_profit: net_profit.saturating_sub(gas_cost),
            gas_used: gas_cost,
            execution_time_ms: 2000, // 假设执行时间
            slippage_bps: Self::calculate_slippage(
                opportunity.expected_profit,
                net_profit,
            ),
        })
    }

    /// 执行跨 DEX 套利
    fn execute_cross_dex_arbitrage<'info>(
        opportunity: &ArbitrageOpportunityData,
        accounts: &'info [AccountInfo<'info>],
        _risk_assessment: &RiskAssessment,
    ) -> Result<ArbitrageExecutionResult> {
        msg!("执行跨 DEX 套利策略");

        // 跨 DEX 套利通常涉及两个方向相反的交易
        require!(
            opportunity.route_config.routes.len() >= 2,
            RouteError::InvalidCrossDexRoute
        );

        // TODO: 实现并发执行两个 DEX 的交易
        // 这里简化为顺序执行
        let actual_output = CircularRouteExecutor::execute_circular_route(
            &opportunity.route_config,
            accounts,
            None,
        )?;

        let net_profit = actual_output.saturating_sub(opportunity.route_config.amount_in);
        let gas_cost = opportunity.estimated_gas_cost;

        Ok(ArbitrageExecutionResult {
            strategy: opportunity.strategy,
            success: net_profit > gas_cost,
            actual_output,
            net_profit: net_profit.saturating_sub(gas_cost),
            gas_used: gas_cost,
            execution_time_ms: 3000,
            slippage_bps: Self::calculate_slippage(
                opportunity.expected_profit,
                net_profit,
            ),
        })
    }

    /// 执行闪电贷套利
    fn execute_flash_loan_arbitrage<'info>(
        opportunity: &ArbitrageOpportunityData,
        accounts: &'info [AccountInfo<'info>],
        risk_assessment: &RiskAssessment,
    ) -> Result<ArbitrageExecutionResult> {
        msg!("执行闪电贷套利策略");

        // 构建闪电贷配置
        let flash_loan_config = FlashLoanConfig {
            provider: 0, // Kamino
            provider_program: *accounts[0].key, // 假设第一个账户是 Kamino 程序
            borrower: *accounts[1].key,         // 假设第二个账户是借贷者
            amount: opportunity.route_config.amount_in,
            max_fee_bps: 20, // 最大 0.2% 费用
        };

        // 构建回调数据（包含套利路由信息）
        let mut callback_data = vec![];
        callback_data.push(opportunity.strategy as u8);
        callback_data.extend_from_slice(&opportunity.route_config.amount_in.to_le_bytes());
        callback_data.push(opportunity.route_config.routes.len() as u8);

        for route in &opportunity.route_config.routes {
            callback_data.push(route.dex_id);
            callback_data.extend_from_slice(&route.input_mint.to_bytes());
            callback_data.extend_from_slice(&route.output_mint.to_bytes());
        }

        // 执行闪电贷
        let flash_loan_result = FlashLoanManager::execute_flash_loan(
            &flash_loan_config,
            accounts,
            &callback_data,
        );

        match flash_loan_result {
            Ok(profit) => {
                let flash_loan_fee = FlashLoanManager::calculate_flash_loan_fee(&flash_loan_config)?;
                let net_profit = profit.saturating_sub(flash_loan_fee);

                Ok(ArbitrageExecutionResult {
                    strategy: opportunity.strategy,
                    success: net_profit > 0,
                    actual_output: opportunity.route_config.amount_in + profit,
                    net_profit,
                    gas_used: opportunity.estimated_gas_cost + 1000, // 闪电贷额外gas
                    execution_time_ms: 4000,
                    slippage_bps: Self::calculate_slippage(
                        opportunity.expected_profit,
                        net_profit,
                    ),
                })
            },
            Err(e) => {
                // 闪电贷失败
                Ok(ArbitrageExecutionResult {
                    strategy: opportunity.strategy,
                    success: false,
                    actual_output: 0,
                    net_profit: 0,
                    gas_used: opportunity.estimated_gas_cost,
                    execution_time_ms: 1000,
                    slippage_bps: 10000, // 100% 滑点表示完全失败
                })
            }
        }
    }

    /// 执行统计套利
    fn execute_statistical_arbitrage<'info>(
        opportunity: &ArbitrageOpportunityData,
        accounts: &'info [AccountInfo<'info>],
        _risk_assessment: &RiskAssessment,
    ) -> Result<ArbitrageExecutionResult> {
        msg!("执行统计套利策略");

        // 统计套利基于历史数据和概率模型
        // 这里简化实现
        let confidence_adjustment = opportunity.confidence_score as f64 / 100.0;
        let adjusted_expected_profit = (opportunity.expected_profit as f64 * confidence_adjustment) as u64;

        // 执行保守的套利策略
        let actual_output = CircularRouteExecutor::execute_circular_route(
            &opportunity.route_config,
            accounts,
            None,
        )?;

        let net_profit = actual_output.saturating_sub(opportunity.route_config.amount_in);
        
        // 统计套利成功标准更严格
        let success = net_profit >= adjusted_expected_profit && 
                     net_profit > opportunity.estimated_gas_cost * 2; // 至少2倍gas成本

        Ok(ArbitrageExecutionResult {
            strategy: opportunity.strategy,
            success,
            actual_output,
            net_profit: net_profit.saturating_sub(opportunity.estimated_gas_cost),
            gas_used: opportunity.estimated_gas_cost,
            execution_time_ms: 2500,
            slippage_bps: Self::calculate_slippage(
                adjusted_expected_profit,
                net_profit,
            ),
        })
    }

    /// 计算滑点
    fn calculate_slippage(expected: u64, actual: u64) -> u16 {
        if expected == 0 {
            return 0;
        }

        if actual >= expected {
            0 // 无滑点或正向滑点
        } else {
            let slippage = ((expected - actual) * 10000 / expected) as u16;
            slippage.min(10000) // 最大100%滑点
        }
    }

    /// 发现套利机会
    pub fn discover_arbitrage_opportunities(
        market_conditions: &MarketConditions,
        min_profit_bps: u16,
        max_amount: u64,
    ) -> Result<Vec<ArbitrageOpportunityData>> {
        let mut opportunities = Vec::new();

        // 扫描三角套利机会
        let triangular_opportunities = Self::scan_triangular_opportunities(
            market_conditions,
            min_profit_bps,
            max_amount,
        )?;
        opportunities.extend(triangular_opportunities);

        // 扫描跨 DEX 套利机会
        let cross_dex_opportunities = Self::scan_cross_dex_opportunities(
            market_conditions,
            min_profit_bps,
            max_amount,
        )?;
        opportunities.extend(cross_dex_opportunities);

        // 按预期利润排序
        opportunities.sort_by(|a, b| b.expected_profit.cmp(&a.expected_profit));

        // 限制返回数量，避免过多机会
        opportunities.truncate(10);

        Ok(opportunities)
    }

    /// 扫描三角套利机会
    fn scan_triangular_opportunities(
        _market_conditions: &MarketConditions,
        min_profit_bps: u16,
        max_amount: u64,
    ) -> Result<Vec<ArbitrageOpportunityData>> {
        // 简化实现：返回模拟的套利机会
        let mut opportunities = Vec::new();

        // 模拟发现一个 SOL -> USDC -> USDT -> SOL 的套利机会
        let base_amount = max_amount / 4; // 使用25%的最大金额
        let expected_profit = base_amount * min_profit_bps as u64 / 10000;

        if expected_profit > 1000 { // 至少1000单位利润
            opportunities.push(ArbitrageOpportunityData {
                strategy: ArbitrageStrategy::Triangular,
                route_config: Self::create_mock_triangular_route(base_amount)?,
                expected_profit,
                confidence_score: 75,
                requires_flash_loan: false,
                estimated_gas_cost: 5000,
                time_sensitivity: TimeSensitivity::Medium,
            });
        }

        Ok(opportunities)
    }

    /// 扫描跨 DEX 套利机会
    fn scan_cross_dex_opportunities(
        _market_conditions: &MarketConditions,
        min_profit_bps: u16,
        max_amount: u64,
    ) -> Result<Vec<ArbitrageOpportunityData>> {
        let mut opportunities = Vec::new();

        // 模拟发现跨 DEX 套利机会
        let base_amount = max_amount / 3;
        let expected_profit = base_amount * (min_profit_bps as u64 + 20) / 10000; // 稍高的利润

        if expected_profit > 2000 {
            opportunities.push(ArbitrageOpportunityData {
                strategy: ArbitrageStrategy::CrossDex,
                route_config: Self::create_mock_cross_dex_route(base_amount)?,
                expected_profit,
                confidence_score: 80,
                requires_flash_loan: base_amount > 50_000_000, // 大额需要闪电贷
                estimated_gas_cost: 7500,
                time_sensitivity: TimeSensitivity::High,
            });
        }

        Ok(opportunities)
    }

    /// 创建模拟三角套利路由
    fn create_mock_triangular_route(amount: u64) -> Result<RouteConfig> {
        use crate::constants::{Route, RoutingMode, Dex};

        let sol_mint = Pubkey::new_unique(); // SOL mint
        let usdc_mint = Pubkey::new_unique(); // USDC mint
        let usdt_mint = Pubkey::new_unique(); // USDT mint

        Ok(RouteConfig {
            routing_mode_id: 1,
            routes: vec![
                Route {
                    dex_id: 1,
                    input_mint: sol_mint,
                    output_mint: usdc_mint,
                },
                Route {
                    dex_id: 2,
                    input_mint: usdc_mint,
                    output_mint: usdt_mint,
                },
                Route {
                    dex_id: 3,
                    input_mint: usdt_mint,
                    output_mint: sol_mint,
                },
            ],
            amount_in: amount,
            min_amount_out: amount + (amount / 200), // 期望0.5%利润
            max_slippage_bps: 100,
            flash_loan: None,
        })
    }

    /// 创建模拟跨 DEX 套利路由
    fn create_mock_cross_dex_route(amount: u64) -> Result<RouteConfig> {
        use crate::constants::{Route, RoutingMode, Dex};

        let sol_mint = Pubkey::new_unique(); // SOL mint
        let usdc_mint = Pubkey::new_unique(); // USDC mint

        Ok(RouteConfig {
            routing_mode_id: 1,
            routes: vec![
                Route {
                    dex_id: 1, // 买入
                    input_mint: usdc_mint,
                    output_mint: sol_mint,
                },
                Route {
                    dex_id: 4,       // 卖出
                    input_mint: sol_mint,
                    output_mint: usdc_mint,
                },
            ],
            amount_in: amount,
            min_amount_out: amount + (amount / 100), // 期望1%利润
            max_slippage_bps: 150,
            flash_loan: None,
        })
    }

    /// 评估套利机会的时效性
    pub fn evaluate_time_sensitivity(
        opportunity: &ArbitrageOpportunityData,
        market_volatility: u16,
    ) -> TimeSensitivity {
        let base_sensitivity = opportunity.time_sensitivity;
        
        match (base_sensitivity, market_volatility) {
            (TimeSensitivity::Low, 0..=200) => TimeSensitivity::Low,
            (TimeSensitivity::Low, 201..=500) => TimeSensitivity::Medium,
            (TimeSensitivity::Low, _) => TimeSensitivity::High,
            
            (TimeSensitivity::Medium, 0..=100) => TimeSensitivity::Low,
            (TimeSensitivity::Medium, 101..=400) => TimeSensitivity::Medium,
            (TimeSensitivity::Medium, _) => TimeSensitivity::High,
            
            (TimeSensitivity::High, _) => TimeSensitivity::High,
        }
    }
}

/// 套利执行结果
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ArbitrageExecutionResult {
    pub strategy: ArbitrageStrategy,
    pub success: bool,
    pub actual_output: u64,
    pub net_profit: u64,
    pub gas_used: u64,
    pub execution_time_ms: u32,
    pub slippage_bps: u16,
}
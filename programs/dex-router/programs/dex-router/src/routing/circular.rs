//! 循环套利路由引擎
//!
//! 实现循环路由模式：A -> B -> C -> A
//! 专门用于套利机会，支持闪电贷零成本套利

use anchor_lang::prelude::*;
use crate::constants::{Route, RouteConfig, FlashLoanConfig, Dex};
use crate::{distribute_swap, KaminoFlashLoanAdapter};
use crate::error::RouteError;
use crate::state::event::{RouteExecuted, ArbitrageOpportunity, SwapCompleted, route_phases};

/// 套利预测结果
#[derive(Debug, Clone)]
pub struct ArbitragePrediction {
    /// 预测利润
    pub predicted_profit: u64,
    /// 总费用
    pub total_fees: u64,
    /// 价格影响
    pub price_impact: u64,
    /// 风险评分 (1-10)
    pub risk_score: u8,
    /// 是否盈利
    pub is_profitable: bool,
    /// 置信度 (0-100%)
    pub confidence_level: u8,
}

/// 循环套利路由执行器
pub struct CircularRouteExecutor;

impl CircularRouteExecutor {
    /// 执行循环套利路由
    pub fn execute_circular_route<'info>(
        config: &RouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
        flash_loan_accounts: Option<&'info [AccountInfo<'info>]>,
    ) -> Result<u64> {
        // 验证路由配置
        Self::validate_circular_config(config)?;

        let initial_token = config.routes[0].input_mint;
        let mut current_amount = config.amount_in;
        let mut total_fees = 0u64;
        let mut executed_routes = Vec::new();

        // 如果使用闪电贷，先执行借贷
        let flash_loan_used = if let Some(flash_config) = &config.flash_loan {
            Self::execute_flash_loan_borrow(flash_config, flash_loan_accounts)?;
            current_amount = flash_config.amount; // 使用借贷金额
            true
        } else {
            false
        };

        // 执行循环路由的每一步
        let mut offset: usize = 0;
        for (step, route) in config.routes.iter().enumerate() {
            msg!("执行循环套利步骤 {}/{}: {:?} -> {:?}",
                step + 1,
                config.routes.len(),
                route.input_mint,
                route.output_mint
            );


            // 执行单步交换 - 直接调用 distribute_swap，与线性路由保持一致
            let amount_out = distribute_swap(
                &route.dex_id.try_into()?,
                remaining_accounts,
                current_amount,
                &mut offset,
                route.input_mint,
                route.output_mint,
                None // 循环套利中通常不需要指定特定的支付者
            )?;

            // 计算交换费用（基于DEX类型估算）
            let step_fee = Self::calculate_swap_fee(&route.dex_id.try_into()?, current_amount)?;

            // 更新状态
            current_amount = amount_out;
            total_fees = total_fees.checked_add(step_fee)
                .ok_or(RouteError::MathOverflow)?;

            executed_routes.push(route.clone());

            // 发出单步完成事件
            emit!(SwapCompleted {
                dex: route.dex_id.try_into()?,
                input_mint: route.input_mint,
                output_mint: route.output_mint,
                amount_in: if step == 0 {
                    if flash_loan_used {
                        config.flash_loan.as_ref().unwrap().amount
                    } else {
                        config.amount_in
                    }
                } else {
                    0
                },
                amount_out,
                fee_paid: step_fee,
                step: step as u8,
            });
        }

        // 处理闪电贷偿还
        let profit = if flash_loan_used {
            let flash_config = config.flash_loan.as_ref().unwrap();
            Self::execute_flash_loan_repay(flash_config, flash_loan_accounts, current_amount)?;

            // 计算利息
            let interest = flash_config.amount.checked_mul(flash_config.max_fee_bps as u64)
                .and_then(|x| x.checked_div(10000))
                .ok_or(RouteError::MathOverflow)?;

            let total_repay = flash_config.amount.checked_add(interest)
                .ok_or(RouteError::MathOverflow)?;

            // 验证能够偿还
            require!(
                current_amount >= total_repay,
                RouteError::InsufficientOutput
            );

            current_amount.checked_sub(total_repay)
                .ok_or(RouteError::MathOverflow)?
        } else {
            // 没有闪电贷，直接计算利润
            require!(
                current_amount >= config.amount_in,
                RouteError::InsufficientOutput
            );

            current_amount.checked_sub(config.amount_in)
                .ok_or(RouteError::MathOverflow)?
        };

        // 发出套利机会事件
        emit!(ArbitrageOpportunity {
            input_token: initial_token,
            amount_in: if flash_loan_used {
                config.flash_loan.as_ref().unwrap().amount
            } else {
                config.amount_in
            },
            amount_out: current_amount,
            profit,
            routes_used: executed_routes.len() as u8,
            flash_loan_used,
        });

        msg!("循环套利执行成功: 利润 {} tokens", profit);

        Ok(profit)
    }

    /// 计算交换费用（基于DEX类型）
    fn calculate_swap_fee(dex: &Dex, amount_in: u64) -> Result<u64> {
        let fee_bps = match dex {
            Dex::RaydiumClmm => 25,    // 0.25%
            Dex::RaydiumCpmm => 25,    // 0.25%
            Dex::MeteoraDlmm => 25,    // 0.25%
            Dex::MeteoraAmm => 30,     // 0.30%
            Dex::Orca => 30,           // 0.30%
            Dex::PumpSwapBuy => 100,   // 1.00%
            Dex::PumpSwapSell => 100,  // 1.00%
        };

        let fee = amount_in.checked_mul(fee_bps as u64)
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        Ok(fee)
    }

    /// 计算实际滑点（基点）
    fn calculate_actual_slippage(amount_in: u64, amount_out: u64) -> Result<u16> {
        if amount_in == 0 {
            return Ok(0);
        }

        // 计算滑点百分比
        let slippage = if amount_out < amount_in {
            let loss = amount_in.checked_sub(amount_out)
                .ok_or(RouteError::MathOverflow)?;
            loss.checked_mul(10000)
                .and_then(|x| x.checked_div(amount_in))
                .ok_or(RouteError::MathOverflow)?
        } else {
            // 如果输出大于输入，滑点为0（实际上是盈利）
            0
        };

        // 确保滑点在合理范围内
        let slippage_bps = (slippage as u16).min(10000);

        Ok(slippage_bps)
    }

    /// 执行闪电贷借贷
    fn execute_flash_loan_borrow<'info>(
        flash_config: &FlashLoanConfig,
        flash_loan_accounts: Option<&'info [AccountInfo<'info>]>,
    ) -> Result<()> {
        let accounts = flash_loan_accounts.ok_or(RouteError::InvalidFlashLoanAccounts)?;

        msg!("开始执行闪电贷借贷: {} tokens, 提供商: {}",
             flash_config.amount, flash_config.provider);

        // 验证闪电贷配置
        require!(
            flash_config.amount > 0,
            RouteError::InvalidFlashLoanConfig
        );

        require!(
            flash_config.max_fee_bps <= 1000, // 最大10%费率
            RouteError::InvalidFlashLoanConfig
        );

        // 根据提供商类型执行闪电贷
        match flash_config.provider {
            0 => {
                // Kamino 闪电贷
                msg!("使用 Kamino 协议执行闪电贷");
                Self::execute_kamino_flash_loan(flash_config, accounts)?;
            },
            1 => {
                // Solend 闪电贷
                msg!("使用 Solend 协议执行闪电贷");
                Self::execute_solend_flash_loan(flash_config, accounts)?;
            },
            _ => {
                msg!("不支持的闪电贷提供商: {}", flash_config.provider);
                return Err(RouteError::UnsupportedFlashLoanProvider.into());
            }
        }

        msg!("闪电贷借贷执行成功");
        Ok(())
    }

    /// 执行 Kamino 闪电贷
    fn execute_kamino_flash_loan<'info>(
        flash_config: &FlashLoanConfig,
        accounts: &'info [AccountInfo<'info>],
    ) -> Result<()> {
        require!(
            accounts.len() >= 8, // Kamino 需要的最少账户数
            RouteError::InvalidFlashLoanAccounts
        );

        // 验证 Kamino 程序 ID
        let kamino_program_id = accounts[0].key;
        require!(
            *kamino_program_id == flash_config.provider_program,
            RouteError::InvalidProgram
        );

        // 构造闪电贷回调数据（包含套利路由信息）
        let callback_data = Self::build_arbitrage_callback_data(flash_config)?;

        // 使用 Kamino 适配器执行闪电贷
        let _result = KaminoFlashLoanAdapter::execute_flash_loan(
            flash_config,
            accounts,
            &callback_data,
        )?;

        msg!("Kamino 闪电贷执行成功");
        Ok(())
    }

    /// 执行 Solend 闪电贷
    fn execute_solend_flash_loan<'info>(
        flash_config: &FlashLoanConfig,
        _accounts: &'info [AccountInfo<'info>],
    ) -> Result<()> {
        // Solend 闪电贷实现（暂时作为占位符）
        msg!("Solend 闪电贷暂未实现，金额: {}", flash_config.amount);

        // 在实际实现中，这里会：
        // 1. 验证 Solend 程序账户
        // 2. 构建 CPI 指令
        // 3. 执行闪电贷借贷

        Ok(())
    }

    /// 执行 Mango 闪电贷
    fn execute_mango_flash_loan<'info>(
        flash_config: &FlashLoanConfig,
        _accounts: &'info [AccountInfo<'info>],
    ) -> Result<()> {
        // Mango 闪电贷实现（暂时作为占位符）
        msg!("Mango 闪电贷暂未实现，金额: {}", flash_config.amount);

        // 在实际实现中，这里会：
        // 1. 验证 Mango 程序账户
        // 2. 构建 CPI 指令
        // 3. 执行闪电贷借贷

        Ok(())
    }

    /// 构建套利回调数据
    fn build_arbitrage_callback_data(flash_config: &FlashLoanConfig) -> Result<Vec<u8>> {
        let mut callback_data = Vec::new();

        // 添加回调类型标识符（0 = 简单套利）
        callback_data.push(0u8);

        // 添加借贷金额
        callback_data.extend_from_slice(&flash_config.amount.to_le_bytes());

        // 添加借贷者公钥
        callback_data.extend_from_slice(&flash_config.borrower.to_bytes());

        // 添加最大费用基点
        callback_data.extend_from_slice(&flash_config.max_fee_bps.to_le_bytes());

        msg!("构建套利回调数据: {} bytes", callback_data.len());

        Ok(callback_data)
    }

    /// 执行闪电贷偿还
    fn execute_flash_loan_repay<'info>(
        flash_config: &FlashLoanConfig,
        flash_loan_accounts: Option<&'info [AccountInfo<'info>]>,
        available_amount: u64,
    ) -> Result<()> {
        let accounts = flash_loan_accounts.ok_or(RouteError::InvalidFlashLoanAccounts)?;

        msg!("开始执行闪电贷偿还: 可用金额: {}, 提供商: {}",
             available_amount, flash_config.provider);

        // 计算实际利息
        let actual_interest = Self::calculate_flash_loan_interest(flash_config, available_amount)?;
        let total_repay = flash_config.amount.checked_add(actual_interest)
            .ok_or(RouteError::MathOverflow)?;

        // 验证是否有足够资金偿还
        require!(
            available_amount >= total_repay,
            RouteError::InsufficientFlashLoanRepay
        );

        msg!("闪电贷偿还计算: 本金: {}, 利息: {}, 总计: {}",
             flash_config.amount, actual_interest, total_repay);

        // 根据提供商类型执行偿还
        match flash_config.provider {
            0 => {
                // Kamino 闪电贷偿还
                msg!("执行 Kamino 闪电贷偿还");
                Self::execute_kamino_flash_loan_repay(flash_config, accounts, total_repay)?;
            },
            1 => {
                // Solend 闪电贷偿还
                msg!("执行 Solend 闪电贷偿还");
                Self::execute_solend_flash_loan_repay(flash_config, accounts, total_repay)?;
            },
            _ => {
                return Err(RouteError::UnsupportedFlashLoanProvider.into());
            }
        }

        msg!("闪电贷偿还执行成功");
        Ok(())
    }

    /// 计算闪电贷利息
    fn calculate_flash_loan_interest(flash_config: &FlashLoanConfig, _available_amount: u64) -> Result<u64> {
        // 使用配置中的最大费率计算利息
        let interest = flash_config.amount.checked_mul(flash_config.max_fee_bps as u64)
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        // 在实际实现中，这里可能会根据：
        // 1. 市场利率
        // 2. 借贷时长
        // 3. 池子利用率
        // 等因素动态计算实际利息

        Ok(interest)
    }

    /// 执行 Kamino 闪电贷偿还
    fn execute_kamino_flash_loan_repay<'info>(
        flash_config: &FlashLoanConfig,
        accounts: &'info [AccountInfo<'info>],
        repay_amount: u64,
    ) -> Result<()> {
        require!(
            accounts.len() >= 8,
            RouteError::InvalidFlashLoanAccounts
        );

        // 验证程序 ID
        require!(
            *accounts[0].key == flash_config.provider_program,
            RouteError::InvalidProgram
        );

        // 在实际实现中，这里会：
        // 1. 构建偿还指令
        // 2. 执行 CPI 调用
        // 3. 转移代币到闪电贷池

        msg!("Kamino 闪电贷偿还完成: {} tokens", repay_amount);
        Ok(())
    }

    /// 执行 Solend 闪电贷偿还
    fn execute_solend_flash_loan_repay<'info>(
        _flash_config: &FlashLoanConfig,
        _accounts: &'info [AccountInfo<'info>],
        repay_amount: u64,
    ) -> Result<()> {
        msg!("Solend 闪电贷偿还暂未实现: {} tokens", repay_amount);
        // 暂时作为占位符
        Ok(())
    }

    /// 预测循环路由的盈利性
    pub fn predict_arbitrage_profitability(config: &RouteConfig) -> Result<ArbitragePrediction> {
        msg!("开始预测循环套利盈利性");

        // 基本验证
        require!(
            config.routes.len() >= 3,
            RouteError::InvalidRouteSteps
        );

        let mut estimated_amount = if let Some(flash_config) = &config.flash_loan {
            flash_config.amount
        } else {
            config.amount_in
        };

        let mut total_fees = 0u64;
        let mut price_impact = 0u64;

        // 模拟每个步骤的交换
        for (i, route) in config.routes.iter().enumerate() {
            msg!("预测步骤 {}: {} -> {}, 数量: {}",
                 i + 1, route.input_mint, route.output_mint, estimated_amount);

            // 计算交换费用
            let step_fee = Self::calculate_swap_fee(&route.dex_id.try_into()?, estimated_amount)?;
            total_fees = total_fees.checked_add(step_fee)
                .ok_or(RouteError::MathOverflow)?;

            // 估算价格影响（基于交换数量和DEX类型）
            let step_price_impact = Self::estimate_price_impact(&route.dex_id.try_into()?, estimated_amount)?;
            price_impact = price_impact.checked_add(step_price_impact)
                .ok_or(RouteError::MathOverflow)?;

            // 模拟输出（简化模型：扣除费用和价格影响）
            let net_amount = estimated_amount.checked_sub(step_fee)
                .and_then(|x| x.checked_sub(step_price_impact))
                .ok_or(RouteError::MathOverflow)?;

            estimated_amount = net_amount;
        }

        // 计算预期利润
        let initial_amount = if let Some(flash_config) = &config.flash_loan {
            // 闪电贷情况：需要扣除利息
            let interest = Self::calculate_flash_loan_interest(flash_config, estimated_amount)?;
            let required_repay = flash_config.amount.checked_add(interest)
                .ok_or(RouteError::MathOverflow)?;

            if estimated_amount > required_repay {
                estimated_amount.checked_sub(required_repay)
                    .ok_or(RouteError::MathOverflow)?
            } else {
                0
            }
        } else {
            // 普通套利：减去初始投入
            if estimated_amount > config.amount_in {
                estimated_amount.checked_sub(config.amount_in)
                    .ok_or(RouteError::MathOverflow)?
            } else {
                0
            }
        };

        let predicted_profit = initial_amount;

        // 计算风险评分
        let risk_score = Self::calculate_arbitrage_risk_score(config, total_fees, price_impact)?;

        let prediction = ArbitragePrediction {
            predicted_profit,
            total_fees,
            price_impact,
            risk_score,
            is_profitable: predicted_profit > 0,
            confidence_level: Self::calculate_confidence_level(config, risk_score)?,
        };

        msg!("套利预测完成: 预期利润: {}, 风险评分: {}, 可盈利: {}",
             predicted_profit, risk_score, prediction.is_profitable);

        Ok(prediction)
    }

    /// 估算价格影响
    fn estimate_price_impact(dex: &Dex, amount: u64) -> Result<u64> {
        // 基于DEX类型和交换数量估算价格影响
        let impact_bps = match dex {
            Dex::RaydiumClmm => {
                // CLMM 池通常有较低的价格影响
                if amount < 1_000_000 { 5 } else if amount < 10_000_000 { 15 } else { 50 }
            },
            Dex::RaydiumCpmm => {
                // CPMM 池价格影响稍高
                if amount < 1_000_000 { 10 } else if amount < 10_000_000 { 30 } else { 100 }
            },
            Dex::MeteoraDlmm => {
                // DLMM 动态费用，影响中等
                if amount < 1_000_000 { 8 } else if amount < 10_000_000 { 25 } else { 80 }
            },
            Dex::MeteoraAmm => {
                // AMM 池，影响较高
                if amount < 1_000_000 { 15 } else if amount < 10_000_000 { 45 } else { 150 }
            },
            Dex::Orca => {
                // Orca 池，影响中等
                if amount < 1_000_000 { 12 } else if amount < 10_000_000 { 35 } else { 120 }
            },
            Dex::PumpSwapBuy | Dex::PumpSwapSell => {
                // PumpFun 池通常流动性较小，价格影响较大
                if amount < 100_000 { 50 } else if amount < 1_000_000 { 200 } else { 500 }
            },
        };

        let impact = amount.checked_mul(impact_bps as u64)
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        Ok(impact)
    }

    /// 计算套利风险评分 (1-10，10为最高风险)
    fn calculate_arbitrage_risk_score(
        config: &RouteConfig,
        total_fees: u64,
        price_impact: u64,
    ) -> Result<u8> {
        let mut risk_score = 1u8;

        // 基于路由步骤数增加风险
        risk_score = risk_score.saturating_add((config.routes.len() as u8).saturating_sub(3));

        // 基于交换金额增加风险
        let amount = if let Some(flash_config) = &config.flash_loan {
            flash_config.amount
        } else {
            config.amount_in
        };

        if amount > 100_000_000 { // > 100 tokens
            risk_score = risk_score.saturating_add(2);
        } else if amount > 10_000_000 { // > 10 tokens
            risk_score = risk_score.saturating_add(1);
        }

        // 基于费用和价格影响增加风险
        let total_cost = total_fees.saturating_add(price_impact);
        if total_cost > amount / 100 { // > 1% 成本
            risk_score = risk_score.saturating_add(2);
        } else if total_cost > amount / 200 { // > 0.5% 成本
            risk_score = risk_score.saturating_add(1);
        }

        // 闪电贷增加风险
        if config.flash_loan.is_some() {
            risk_score = risk_score.saturating_add(1);
        }

        // 确保风险评分在合理范围内
        risk_score = risk_score.min(10);

        Ok(risk_score)
    }

    /// 计算预测置信度
    fn calculate_confidence_level(config: &RouteConfig, risk_score: u8) -> Result<u8> {
        let mut confidence = 90u8; // 基础置信度90%

        // 基于风险评分降低置信度
        confidence = confidence.saturating_sub(risk_score * 5);

        // 基于路由复杂度降低置信度
        confidence = confidence.saturating_sub((config.routes.len() as u8).saturating_sub(3) * 3);

        // 闪电贷降低置信度
        if config.flash_loan.is_some() {
            confidence = confidence.saturating_sub(10);
        }

        // 确保置信度在合理范围内
        confidence = confidence.max(10).min(95);

        Ok(confidence)
    }

    /// 验证循环路由配置
    pub fn validate_circular_config(config: &RouteConfig) -> Result<()> {

        require!(
            config.routes.len() >= 3 && config.routes.len() <= 6,
            RouteError::InvalidRouteSteps
        );

        require!(
            config.amount_in > 0,
            RouteError::ZeroAmount
        );

        // 验证路由形成闭环：最后输出应该等于第一个输入
        let first_input = config.routes[0].input_mint;
        let last_output = config.routes.last().unwrap().output_mint;
        require!(
            first_input == last_output,
            RouteError::InvalidCircularRoute
        );

        // 验证路由连续性
        for i in 1..config.routes.len() {
            require!(
                config.routes[i - 1].output_mint == config.routes[i].input_mint,
                RouteError::InvalidRouteConfig
            );
        }

        // 如果使用闪电贷，验证配置
        if let Some(flash_config) = &config.flash_loan {
            require!(
                flash_config.amount > 0,
                RouteError::InvalidFlashLoanConfig
            );

            require!(
                flash_config.max_fee_bps <= 1000, // 最大10%利率
                RouteError::InvalidFlashLoanConfig
            );
        }

        Ok(())
    }
}

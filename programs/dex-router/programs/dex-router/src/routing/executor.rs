//! 统一路由执行器
//!
//! 提供统一的路由执行接口，根据路由模式调用相应的执行器

use anchor_lang::prelude::*;
use crate::constants::{RouteConfig, RoutingMode, BranchRouteConfig, BatchRouteConfig};
use crate::error::RouteError;
use crate::state::event::{RouteExecuted, route_phases};
use crate::routing::{
    LinearRouteExecutor,
    CircularRouteExecutor,
    BranchingRouteExecutor,
    BatchedRouteExecutor,
    BatchExecutionResult,
};

/// 统一路由执行器
/// 根据路由配置自动选择合适的执行器
pub struct RouteExecutor;

impl RouteExecutor {
    /// 执行路由配置
    pub fn execute_route<'info>(
        config: &RouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
        payer: &AccountInfo<'info>,
    ) -> Result<u64> {
        // 验证并转换路由模式
        let routing_mode = config.validate_and_get_routing_mode()?;

        // 发出执行开始事件
        emit!(RouteExecuted {
            phase: route_phases::STARTED,
            mode: routing_mode.clone(),
            user: None,
            amount_in: config.amount_in,
            amount_out: 0,
            min_amount_out: config.min_amount_out,
            routes_count: config.routes.len() as u8,
            routes_executed: 0,
            total_fees: 0,
            execution_time: 0,
            success: false,
            actual_slippage_bps: 0,
            timestamp: Clock::get()?.unix_timestamp,
        });

        let start_time = Clock::get()?.unix_timestamp;

        // 根据路由模式选择执行器
        let result = match routing_mode {
            RoutingMode::Linear => {
                msg!("执行线性路由");
                LinearRouteExecutor::execute_linear_route(config, remaining_accounts, payer)
            },
            RoutingMode::Circular => {
                msg!("执行循环套利路由");
                CircularRouteExecutor::execute_circular_route(
                    config,
                    remaining_accounts,
                    None // 暂不支持闪电贷账户
                )
            },
            RoutingMode::Branching => {
                return Err(RouteError::UnsupportedRouteMode.into());
            },
            RoutingMode::Batched => {
                return Err(RouteError::UnsupportedRouteMode.into());
            },
        };

        let end_time = Clock::get()?.unix_timestamp;
        let execution_time = end_time - start_time;

        match result {
            Ok(amount_out) => {
                // 发出执行完成事件
                let total_fees = 0;
                let actual_slippage_bps = if config.amount_in > 0 {
                    let expected_out = (config.amount_in as f64 * 0.997) as u64; // 假设 0.3% 费率
                    if expected_out > amount_out {
                        (((expected_out - amount_out) * 10000) / expected_out) as u16
                    } else { 0 }
                } else { 0 };

                emit!(RouteExecuted {
                    phase: route_phases::COMPLETED,
                    mode: routing_mode.clone(),
                    user: None,
                    amount_in: config.amount_in,
                    amount_out,
                    min_amount_out: config.min_amount_out,
                    routes_count: config.routes.len() as u8,
                    routes_executed: config.routes.len() as u8,
                    total_fees,
                    execution_time: execution_time as u32,
                    success: true,
                    actual_slippage_bps,
                    timestamp: end_time,
                });

                Ok(amount_out)
            },
            Err(e) => {
                // 发出执行失败事件
                emit!(RouteExecuted {
                    phase: route_phases::FAILED,
                    mode: routing_mode.clone(),
                    user: None,
                    amount_in: config.amount_in,
                    amount_out: 0,
                    min_amount_out: config.min_amount_out,
                    routes_count: config.routes.len() as u8,
                    routes_executed: 0,
                    total_fees: 0,
                    execution_time: execution_time as u32,
                    success: false,
                    actual_slippage_bps: 0,
                    timestamp: end_time,
                });

                Err(e)
            }
        }
    }

    /// 执行分支路由
    pub fn execute_branch_route<'info>(
        config: &BranchRouteConfig,
        accounts: &[AccountInfo<'info>],
        remaining_accounts: &[AccountInfo<'info>],
    ) -> Result<u64> {
        msg!("执行分支路由");
        BranchingRouteExecutor::execute_branching_route(config, accounts, remaining_accounts)
    }

    /// 执行批量路由
    pub fn execute_batch_route<'info>(
        config: &BatchRouteConfig,
        accounts: &[AccountInfo<'info>],
        remaining_accounts: &[AccountInfo<'info>],
    ) -> Result<BatchExecutionResult> {
        msg!("执行批量路由");
        BatchedRouteExecutor::execute_batched_route(config, accounts, remaining_accounts)
    }

    /// 验证路由配置
    pub fn validate_route_config(config: &RouteConfig) -> Result<()> {
        // 验证并获取路由模式
        let routing_mode = config.validate_and_get_routing_mode()?;

        match routing_mode {
            RoutingMode::Linear => {
                LinearRouteExecutor::validate_linear_config(config)
            },
            RoutingMode::Circular => {
                CircularRouteExecutor::validate_circular_config(config)
            },
            RoutingMode::Branching => {
                Err(RouteError::UnsupportedRouteMode.into())
            },
            RoutingMode::Batched => {
                Err(RouteError::UnsupportedRouteMode.into())
            },
        }
    }

    /// 验证分支路由配置
    pub fn validate_branch_config(config: &BranchRouteConfig) -> Result<()> {
        BranchingRouteExecutor::validate_branching_config(config)
    }

    /// 验证批量路由配置
    pub fn validate_batch_config(config: &BatchRouteConfig) -> Result<()> {
        BatchedRouteExecutor::validate_batched_config(config)
    }
}

/// 路由执行上下文
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RouteExecutionContext {
    pub user: Pubkey,
    pub input_token: Pubkey,
    pub output_token: Pubkey,
    pub amount_in: u64,
    pub min_amount_out: u64,
    pub max_slippage_bps: u16,
    pub deadline: i64,
    pub priority_fee: Option<u64>,
}

impl RouteExecutionContext {
    /// 验证执行上下文
    pub fn validate(&self) -> Result<()> {
        require!(
            self.amount_in > 0,
            RouteError::ZeroAmount
        );

        require!(
            self.min_amount_out > 0,
            RouteError::ZeroAmount
        );

        require!(
            self.max_slippage_bps <= 1000, // 最大10%滑点
            RouteError::InvalidSlippage
        );

        let current_time = Clock::get()?.unix_timestamp;
        require!(
            self.deadline > current_time,
            RouteError::DeadlineExceeded
        );

        Ok(())
    }

    /// 检查是否超时
    pub fn is_expired(&self) -> Result<bool> {
        let current_time = Clock::get()?.unix_timestamp;
        Ok(current_time > self.deadline)
    }
}

/// 路由执行监控器
pub struct RouteMonitor;

impl RouteMonitor {
    /// 检查路由执行状态
    pub fn check_route_health(config: &RouteConfig) -> Result<RouteHealthStatus> {
        let mut warnings = Vec::new();
        let mut errors = Vec::new();

        // 检查滑点设置
        if config.max_slippage_bps > 500 {
            warnings.push("高滑点设置可能导致显著损失".to_string());
        }

        // 检查路由复杂度
        if config.routes.len() > 4 {
            warnings.push("路由步骤过多可能增加失败风险".to_string());
        }

        // 检查金额大小
        if config.amount_in < 1000 {
            errors.push("交换金额过小".to_string());
        }

        // 检查输出要求的合理性
        let efficiency = (config.min_amount_out as f64) / (config.amount_in as f64);
        if efficiency < 0.5 {
            errors.push("预期输出过低".to_string());
        }

        let health_score = if !errors.is_empty() {
            0
        } else if warnings.len() > 2 {
            25
        } else if warnings.len() > 0 {
            75
        } else {
            100
        };

        let is_healthy = errors.is_empty() && warnings.len() <= 1;

        Ok(RouteHealthStatus {
            health_score,
            warnings,
            errors,
            is_healthy,
        })
    }
}

/// 路由健康状态
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RouteHealthStatus {
    pub health_score: u8, // 0-100
    pub warnings: Vec<String>,
    pub errors: Vec<String>,
    pub is_healthy: bool,
}

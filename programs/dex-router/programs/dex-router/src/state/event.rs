//! 统一事件定义
//!
//! 定义智能合约发出的所有事件，包括路由、套利、闪电贷等

use anchor_lang::prelude::*;
use crate::constants::{RoutingMode, Dex};

// ===== 配置和初始化事件 =====

/// 路由器配置初始化事件
#[event]
pub struct ConfigInitialized {
    pub admin: Pubkey,
    pub supported_dex_flags: u64,
    pub max_route_amount: u64,
    pub default_slippage_bps: u16,
}

/// 用户位置初始化事件
#[event]
pub struct UserPositionInitialized {
    pub user: Pubkey,
    pub position_account: Pubkey,
}

/// 配置更新事件
#[event]
pub struct ConfigUpdated {
    /// 管理员公钥
    pub admin: Pubkey,
    
    /// 更新的字段
    pub field: String,
    
    /// 旧值
    pub old_value: String,
    
    /// 新值
    pub new_value: String,
    
    /// 时间戳
    pub timestamp: i64,
}

// ===== 路由执行事件 =====

/// 统一路由执行事件（合并 RouteStarted, RouteExecuted, RouteExecutionStarted, RouteExecutionCompleted）
#[event]
pub struct RouteExecuted {
    /// 执行阶段：0=开始，1=完成，2=失败
    pub phase: u8,
    
    /// 路由模式
    pub mode: RoutingMode,
    
    /// 用户公钥
    pub user: Option<Pubkey>,
    
    /// 输入金额
    pub amount_in: u64,
    
    /// 输出金额（完成时有效）
    pub amount_out: u64,
    
    /// 最小预期输出
    pub min_amount_out: u64,
    
    /// 路由步骤数
    pub routes_count: u8,
    
    /// 成功执行的路由数
    pub routes_executed: u8,
    
    /// 总手续费
    pub total_fees: u64,
    
    /// 执行时间（毫秒）
    pub execution_time: u32,
    
    /// 是否成功
    pub success: bool,
    
    /// 实际滑点（基点）
    pub actual_slippage_bps: u16,
    
    /// 时间戳
    pub timestamp: i64,
}

/// 单步交换完成事件
#[event]
pub struct SwapCompleted {
    pub dex: Dex,
    pub input_mint: Pubkey,
    pub output_mint: Pubkey,
    pub amount_in: u64,
    pub amount_out: u64,
    pub fee_paid: u64,
    pub step: u8,
}

// ===== 套利和分支执行事件 =====

/// 套利机会发现事件
#[event]
pub struct ArbitrageOpportunity {
    pub input_token: Pubkey,
    pub amount_in: u64,
    pub amount_out: u64,
    pub profit: u64,
    pub routes_used: u8,
    pub flash_loan_used: bool,
}

/// 分支执行完成事件
#[event]
pub struct BranchExecuted {
    pub branch_index: u8,
    pub input_token: Pubkey,
    pub output_token: Pubkey,
    pub amount_in: u64,
    pub amount_out: u64,
    pub allocation_bps: u16,
    pub routes_in_branch: u8,
}

/// 批量执行完成事件
#[event]
pub struct BatchExecuted {
    pub total_routes: u8,
    pub successful_routes: u8,
    pub failed_routes: u8,
    pub total_amount_in: u64,
    pub total_amount_out: u64,
    pub total_fees: u64,
    pub atomic_mode: bool,
}

// ===== 闪电贷事件（统一 FlashLoanExecuted 和 BasicFlashLoanExecuted） =====

/// 统一闪电贷执行事件
#[event]
pub struct FlashLoanExecuted {
    /// 用户公钥（可选，某些情况下可能没有）
    pub user: Option<Pubkey>,
    
    /// 闪电贷提供者
    pub provider: Pubkey,
    
    /// 借贷金额
    pub amount: u64,
    
    /// 费用
    pub fee: u64,
    
    /// 是否成功
    pub success: bool,
    
    /// 利润（如果是套利，可为负数表示亏损）
    pub profit: i64,
    
    /// 时间戳
    pub timestamp: i64,
}

// ===== 风险和安全事件（统一 RiskAssessment 和 SecurityAlert） =====

/// 统一安全和风险评估事件
#[event]
pub struct SecurityEvent {
    /// 事件类型：0=风险评估，1=安全警报，2=价格影响警告
    pub event_type: u8,
    
    /// 严重程度：1=信息，2=警告，3=错误，4=严重
    pub severity: u8,
    
    /// 相关用户
    pub user: Option<Pubkey>,
    
    /// DEX（价格影响警告时使用）
    pub dex: Option<Dex>,
    
    /// 输入代币（价格影响警告时使用）
    pub input_mint: Option<Pubkey>,
    
    /// 输出代币（价格影响警告时使用）
    pub output_mint: Option<Pubkey>,
    
    /// 金额
    pub amount: u64,
    
    /// 最大允许金额或建议最大金额
    pub max_allowed_amount: u64,
    
    /// 风险评分或价格影响（基点）
    pub score_or_impact_bps: u16,
    
    /// 是否通过评估
    pub approved: bool,
    
    /// 事件描述
    pub description: String,
    
    /// 时间戳
    pub timestamp: i64,
}

// ===== 系统和监控事件 =====

/// 性能指标事件
#[event]
pub struct PerformanceMetrics {
    /// 平均执行时间（毫秒）
    pub avg_execution_time_ms: u64,
    
    /// 平均Gas消耗
    pub avg_gas_used: u64,
    
    /// 成功率（基点）
    pub success_rate_bps: u16,
    
    /// 平均滑点（基点）
    pub avg_slippage_bps: u16,
    
    /// 统计周期内的交易数
    pub trade_count: u32,
    
    /// 统计周期（秒）
    pub period_seconds: u32,
    
    /// 时间戳
    pub timestamp: i64,
}

/// DEX操作日志事件
#[event]
pub struct DexOperationLogged {
    /// DEX类型
    pub dex_type: u8,
    
    /// 操作类型
    pub operation: String,
    
    /// 输入代币
    pub input_mint: Pubkey,
    
    /// 输出代币
    pub output_mint: Pubkey,
    
    /// 输入金额
    pub amount_in: u64,
    
    /// 输出金额
    pub amount_out: u64,
    
    /// 是否成功
    pub success: bool,
    
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
    
    /// 时间戳
    pub timestamp: i64,
}

/// 用户等级升级事件
#[event]
pub struct UserLevelUp {
    /// 用户公钥
    pub user: Pubkey,
    
    /// 旧等级
    pub old_level: u8,
    
    /// 新等级
    pub new_level: u8,
    
    /// 总交易次数
    pub total_trades: u32,
    
    /// 成功率（基点）
    pub success_rate_bps: u16,
    
    /// 时间戳
    pub timestamp: i64,
}

/// 紧急停止事件
#[event]
pub struct EmergencyStopTriggered {
    /// 触发者
    pub triggered_by: Pubkey,
    
    /// 停止类型（0=全局，1=单个DEX）
    pub stop_type: u8,
    
    /// 受影响的DEX（如果是单个DEX停止）
    pub affected_dex: Option<u8>,
    
    /// 停止原因
    pub reason: String,
    
    /// 时间戳
    pub timestamp: i64,
}

/// 紧急恢复事件
#[event]
pub struct EmergencyRecovered {
    /// 触发者
    pub triggered_by: Pubkey,
    
    /// 恢复类型（0=全局，1=单个DEX）
    pub recovery_type: u8,
    
    /// 恢复的DEX（如果是单个DEX恢复）
    pub recovered_dex: Option<u8>,
    
    /// 停止持续时间（秒）
    pub stop_duration_seconds: u64,
    
    /// 时间戳
    pub timestamp: i64,
}

/// 利润分配事件
#[event]
pub struct ProfitDistributed {
    /// 用户公钥
    pub user: Pubkey,
    
    /// 总利润
    pub total_profit: u64,
    
    /// 用户份额
    pub user_share: u64,
    
    /// 协议费用
    pub protocol_fee: u64,
    
    /// 其他费用
    pub other_fees: u64,
    
    /// 时间戳
    pub timestamp: i64,
}

// ===== 事件类型和严重程度常量 =====

/// 路由执行阶段常量
pub mod route_phases {
    pub const STARTED: u8 = 0;
    pub const COMPLETED: u8 = 1;
    pub const FAILED: u8 = 2;
}

/// 安全事件类型常量
pub mod security_event_types {
    pub const RISK_ASSESSMENT: u8 = 0;
    pub const SECURITY_ALERT: u8 = 1;
    pub const PRICE_IMPACT_WARNING: u8 = 2;
}

/// 紧急操作类型常量
pub mod emergency_operations {
    pub const STOP: u8 = 0;
    pub const RECOVER: u8 = 1;
}

/// 操作范围常量
pub mod operation_scopes {
    pub const GLOBAL: u8 = 0;
    pub const SINGLE_DEX: u8 = 1;
}

/// 警报类型常量
pub mod alert_types {
    pub const SUSPICIOUS_ACTIVITY: u8 = 1;
    pub const HIGH_RISK_TRANSACTION: u8 = 2;
    pub const RATE_LIMIT_VIOLATION: u8 = 3;
    pub const UNUSUAL_PROFIT: u8 = 4;
    pub const SYSTEM_ANOMALY: u8 = 5;
    pub const PRICE_MANIPULATION: u8 = 6;
}

/// 严重程度常量
pub mod severity_levels {
    pub const INFO: u8 = 1;
    pub const WARNING: u8 = 2;
    pub const ERROR: u8 = 3;
    pub const CRITICAL: u8 = 4;
}
//! 简化的路由测试
//!
//! 测试基础路由功能的正确性

#[cfg(test)]
mod routing_tests {
    use anchor_lang::prelude::*;

    /// 基础类型测试
    #[test]
    fn test_basic_route_types() {
        // 测试DEX枚举
        use onchain_router::routing::types::Dex;

        let dex = Dex::RaydiumCpmm;
        assert!(matches!(dex, Dex::RaydiumCpmm));

        let dex2 = Dex::Orca;
        assert!(matches!(dex2, Dex::Orca));
    }

    /// 路由模式测试
    #[test]
    fn test_routing_modes() {
        use onchain_router::routing::types::RoutingMode;

        let mode = RoutingMode::Linear;
        assert!(matches!(mode, RoutingMode::Linear));

        let circular_mode = RoutingMode::Circular;
        assert!(matches!(circular_mode, RoutingMode::Circular));

        let branch_mode = RoutingMode::Branching;
        assert!(matches!(branch_mode, RoutingMode::Branching));

        let batch_mode = RoutingMode::Batched;
        assert!(matches!(batch_mode, RoutingMode::Batched));
    }

    /// 路由配置验证测试
    #[test]
    fn test_route_validation() {
        use onchain_router::routing::types::{Route, RouteConfig, RoutingMode, Dex};

        let mint_a = Pubkey::new_unique();
        let mint_b = Pubkey::new_unique();

        // 创建基础路由
        let route = Route {
            dex: Dex::RaydiumCpmm,
            input_mint: mint_a,
            output_mint: mint_b,
            swap_data: vec![0u8; 32],
            min_amount_out: 1000,
        };

        // 验证路由
        assert!(route.validate().is_ok());

        // 创建路由配置
        let route_config = RouteConfig {
            mode: RoutingMode::Linear,
            routes: vec![route],
            amount_in: 100_000,
            min_amount_out: 90_000,
            flash_loan: None,
            max_slippage_bps: 300,
        };

        // 验证路由配置
        assert!(route_config.validate().is_ok());
    }

    /// 多步路由验证测试
    #[test]
    fn test_multi_step_route_validation() {
        use onchain_router::routing::types::{Route, RouteConfig, RoutingMode, Dex};

        let mint_a = Pubkey::new_unique();
        let mint_b = Pubkey::new_unique();
        let mint_c = Pubkey::new_unique();

        // 创建连续的多步路由
        let routes = vec![
            Route {
                dex: Dex::RaydiumCpmm,
                input_mint: mint_a,
                output_mint: mint_b,
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
            Route {
                dex: Dex::Orca,
                input_mint: mint_b,
                output_mint: mint_c,
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
        ];

        let route_config = RouteConfig {
            mode: RoutingMode::Linear,
            routes,
            amount_in: 100_000,
            min_amount_out: 80_000,
            flash_loan: None,
            max_slippage_bps: 500,
        };

        // 验证多步路由配置
        assert!(route_config.validate().is_ok());
    }

    /// 循环路由验证测试
    #[test]
    fn test_circular_route_validation() {
        use onchain_router::routing::types::{Route, RouteConfig, RoutingMode, Dex};

        let mint_a = Pubkey::new_unique();
        let mint_b = Pubkey::new_unique();
        let mint_c = Pubkey::new_unique();

        // 创建循环路由: A -> B -> C -> A
        let routes = vec![
            Route {
                dex: Dex::RaydiumCpmm,
                input_mint: mint_a,
                output_mint: mint_b,
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
            Route {
                dex: Dex::Orca,
                input_mint: mint_b,
                output_mint: mint_c,
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
            Route {
                dex: Dex::MeteoraAmm,
                input_mint: mint_c,
                output_mint: mint_a, // 回到起始代币
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
        ];

        let route_config = RouteConfig {
            mode: RoutingMode::Circular,
            routes,
            amount_in: 100_000,
            min_amount_out: 100_000, // 套利至少要保本
            flash_loan: None,
            max_slippage_bps: 1000,
        };

        // 验证循环路由配置
        assert!(route_config.validate().is_ok());
    }

    /// 无效路由测试
    #[test]
    fn test_invalid_routes() {
        use onchain_router::routing::types::{Route, RouteConfig, RoutingMode, Dex};

        let mint_a = Pubkey::new_unique();
        let mint_b = Pubkey::new_unique();
        let mint_c = Pubkey::new_unique();

        // 1. 测试不连续的路由
        let discontinuous_routes = vec![
            Route {
                dex: Dex::RaydiumCpmm,
                input_mint: mint_a,
                output_mint: mint_b,
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
            Route {
                dex: Dex::Orca,
                input_mint: mint_c, // 错误：应该是mint_b
                output_mint: mint_a,
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
        ];

        let bad_config = RouteConfig {
            mode: RoutingMode::Linear,
            routes: discontinuous_routes,
            amount_in: 100_000,
            min_amount_out: 80_000,
            flash_loan: None,
            max_slippage_bps: 300,
        };

        // 应该验证失败
        assert!(bad_config.validate().is_err());

        // 2. 测试无效的循环路由（不回到起始代币）
        let invalid_circular_routes = vec![
            Route {
                dex: Dex::RaydiumCpmm,
                input_mint: mint_a,
                output_mint: mint_b,
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
            Route {
                dex: Dex::Orca,
                input_mint: mint_b,
                output_mint: mint_c, // 错误：应该回到mint_a
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
        ];

        let bad_circular_config = RouteConfig {
            mode: RoutingMode::Circular,
            routes: invalid_circular_routes,
            amount_in: 100_000,
            min_amount_out: 100_000,
            flash_loan: None,
            max_slippage_bps: 500,
        };

        // 应该验证失败
        assert!(bad_circular_config.validate().is_err());
    }

    /// 复杂度评分测试
    #[test]
    fn test_complexity_scoring() {
        use onchain_router::routing::types::{Route, RouteConfig, RoutingMode, Dex};

        let mint_a = Pubkey::new_unique();
        let mint_b = Pubkey::new_unique();
        let mint_c = Pubkey::new_unique();
        let mint_d = Pubkey::new_unique();

        // 简单路由
        let simple_routes = vec![
            Route {
                dex: Dex::PumpSwap,
                input_mint: mint_a,
                output_mint: mint_b,
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
        ];

        let simple_config = RouteConfig {
            mode: RoutingMode::Linear,
            routes: simple_routes,
            amount_in: 100_000,
            min_amount_out: 90_000,
            flash_loan: None,
            max_slippage_bps: 300,
        };

        // 复杂路由
        let complex_routes = vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: mint_a,
                output_mint: mint_b,
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
            Route {
                dex: Dex::MeteoraLb,
                input_mint: mint_b,
                output_mint: mint_c,
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
            Route {
                dex: Dex::Orca,
                input_mint: mint_c,
                output_mint: mint_d,
                swap_data: vec![0u8; 32],
                min_amount_out: 0,
            },
        ];

        let complex_config = RouteConfig {
            mode: RoutingMode::Branching,
            routes: complex_routes,
            amount_in: 100_000,
            min_amount_out: 70_000,
            flash_loan: None,
            max_slippage_bps: 800,
        };

        let simple_score = simple_config.complexity_score();
        let complex_score = complex_config.complexity_score();

        assert!(complex_score > simple_score, "复杂路由的评分应该更高");
        assert!(simple_score >= 1, "简单路由的评分应该至少为1");
        assert!(complex_score >= 3, "复杂路由的评分应该至少为3");
    }
}

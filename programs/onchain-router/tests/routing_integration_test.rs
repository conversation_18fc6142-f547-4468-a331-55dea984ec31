//! 路由模式集成测试
//!
//! 测试所有路由模式的功能完整性和集成性

#[allow(unused_imports)]
use anchor_lang::prelude::*;

// 仅在测试时导入路由模块
#[cfg(test)]
use onchain_router::routing::types::*;

/// 创建测试用的Route实例
fn create_test_route(
    dex: Dex,
    input_mint: Pubkey,
    output_mint: Pubkey,
    min_amount_out: u64,
) -> Route {
    Route {
        dex,
        input_mint,
        output_mint,
        swap_data: vec![0u8; 32], // 模拟swap数据
        min_amount_out,
    }
}

/// 创建测试代币mint地址
fn create_test_mints() -> (Pubkey, Pubkey, Pubkey, Pubkey) {
    let mint_a = Pubkey::new_unique(); // USDC
    let mint_b = Pubkey::new_unique(); // SOL
    let mint_c = Pubkey::new_unique(); // BTC
    let mint_d = Pubkey::new_unique(); // ETH
    (mint_a, mint_b, mint_c, mint_d)
}

#[cfg(test)]
mod linear_route_tests {
    use super::*;

    #[test]
    fn test_linear_route_validation() {
        let (mint_a, mint_b, mint_c, _) = create_test_mints();

        // 创建有效的线性路由: A -> B -> C
        let routes = vec![
            create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
            create_test_route(Dex::Orca, mint_b, mint_c, 0),
        ];

        // 验证路由配置
        let result = LinearRouteExecutor::validate_route_feasibility(&routes, 100_000);
        assert!(result.is_ok(), "线性路由验证应该成功");

        // 估算输出
        let estimated_output = LinearRouteExecutor::calculate_output(&routes, 100_000);
        assert!(estimated_output.is_ok(), "线性路由输出估算应该成功");

        let output = estimated_output.unwrap();
        assert!(output > 0, "预期输出应该大于0");
        assert!(output < 100_000, "考虑交易费用，输出应该小于输入");
    }

    #[test]
    fn test_linear_route_gas_estimation() {
        let (mint_a, mint_b, mint_c, _) = create_test_mints();

        let routes = vec![
            create_test_route(Dex::RaydiumClmm, mint_a, mint_b, 0),
            create_test_route(Dex::MeteoraLb, mint_b, mint_c, 0),
        ];

        let gas_cost = LinearRouteExecutor::estimate_gas_cost(&routes);
        assert!(gas_cost > 0, "Gas估算应该大于0");
        assert!(gas_cost < 1_000_000, "Gas估算应该在合理范围内");
    }

    #[test]
    fn test_linear_route_discontinuity() {
        let (mint_a, mint_b, mint_c, mint_d) = create_test_mints();

        // 创建不连续的路由: A -> B, C -> D（中间断开）
        let routes = vec![
            create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
            create_test_route(Dex::Orca, mint_c, mint_d, 0), // 错误：mint_c != mint_b
        ];

        let result = LinearRouteExecutor::validate_route_feasibility(&routes, 100_000);
        assert!(result.is_err(), "不连续的路由应该验证失败");
    }
}

#[cfg(test)]
mod circular_route_tests {
    use super::*;

    #[test]
    fn test_circular_route_validation() {
        let (mint_a, mint_b, mint_c, _) = create_test_mints();

        // 创建有效的循环路由: A -> B -> C -> A
        let routes = vec![
            create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
            create_test_route(Dex::Orca, mint_b, mint_c, 0),
            create_test_route(Dex::MeteoraAmm, mint_c, mint_a, 0), // 回到起始代币
        ];

        // 验证流动性
        let result = CircularRouteExecutor::validate_liquidity(&routes, 100_000);
        assert!(result.is_ok(), "循环路由流动性验证应该成功");

        // 估算利润
        let profit = CircularRouteExecutor::estimate_profit(&routes, 100_000, None);
        assert!(profit.is_ok(), "循环路由利润估算应该成功");
    }

    #[test]
    fn test_circular_route_profitability() {
        let (mint_a, mint_b, mint_c, _) = create_test_mints();

        let routes = vec![
            create_test_route(Dex::PumpSwap, mint_a, mint_b, 0),
            create_test_route(Dex::RaydiumClmm, mint_b, mint_c, 0),
            create_test_route(Dex::Orca, mint_c, mint_a, 0),
        ];

        // 检查套利可行性
        let is_profitable = CircularRouteExecutor::is_profitable(
            &routes,
            100_000,
            1000, // 最小利润阈值
            None,
        );
        assert!(is_profitable.is_ok(), "套利可行性检查应该成功");
    }

    #[test]
    fn test_circular_route_with_flash_loan() {
        let (mint_a, mint_b, mint_c, _) = create_test_mints();

        let routes = vec![
            create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
            create_test_route(Dex::MeteoraLb, mint_b, mint_c, 0),
            create_test_route(Dex::Orca, mint_c, mint_a, 0),
        ];

        let flash_config = FlashLoanConfig {
            provider: Pubkey::new_unique(),
            amount: 50_000,
            max_fee_bps: 30, // 0.3%
            collateral_mint: None,
        };

        let profit = CircularRouteExecutor::estimate_profit(&routes, 50_000, Some(&flash_config));
        assert!(profit.is_ok(), "闪电贷套利利润估算应该成功");
    }

    #[test]
    fn test_invalid_circular_route() {
        let (mint_a, mint_b, mint_c, mint_d) = create_test_mints();

        // 创建无效的循环路由: A -> B -> C -> D（不回到起始代币）
        let routes = vec![
            create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
            create_test_route(Dex::Orca, mint_b, mint_c, 0),
            create_test_route(Dex::MeteoraAmm, mint_c, mint_d, 0), // 错误：应该回到mint_a
        ];

        let result = CircularRouteExecutor::validate_liquidity(&routes, 100_000);
        assert!(result.is_err(), "无效的循环路由应该验证失败");
    }
}

#[cfg(test)]
mod branch_route_tests {
    use super::*;

    #[test]
    fn test_branch_route_validation() {
        let (mint_a, mint_b, mint_c, mint_d) = create_test_mints();

        // 创建分支路由配置: A -> [B, C] -> D
        let branch_config = BranchRouteConfig {
            input_distribution: vec![6000, 4000], // 60% + 40% = 100%
            branch_routes: vec![
                // 分支1: A -> B -> D
                vec![
                    create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
                    create_test_route(Dex::Orca, mint_b, mint_d, 0),
                ],
                // 分支2: A -> C -> D
                vec![
                    create_test_route(Dex::MeteoraAmm, mint_a, mint_c, 0),
                    create_test_route(Dex::PumpSwap, mint_c, mint_d, 0),
                ],
            ],
            target_mint: mint_d,
        };

        let result = BranchingRouteExecutor::validate_config(&branch_config);
        assert!(result.is_ok(), "分支路由配置验证应该成功");

        // 估算总输出
        let output = BranchingRouteExecutor::estimate_total_output(&branch_config, 100_000);
        assert!(output.is_ok(), "分支路由输出估算应该成功");
    }

    #[test]
    fn test_branch_route_gas_estimation() {
        let (mint_a, mint_b, mint_c, mint_d) = create_test_mints();

        let branch_config = BranchRouteConfig {
            input_distribution: vec![5000, 3000, 2000], // 50% + 30% + 20%
            branch_routes: vec![
                vec![create_test_route(Dex::RaydiumCpmm, mint_a, mint_d, 0)],
                vec![create_test_route(Dex::Orca, mint_a, mint_d, 0)],
                vec![create_test_route(Dex::MeteoraAmm, mint_a, mint_d, 0)],
            ],
            target_mint: mint_d,
        };

        let gas_cost = BranchingRouteExecutor::estimate_gas_cost(&branch_config);
        assert!(gas_cost > 0, "分支路由Gas估算应该大于0");
    }

    #[test]
    fn test_invalid_branch_distribution() {
        let (mint_a, mint_b, _, mint_d) = create_test_mints();

        // 错误的分配比例（总和不等于10000）
        let branch_config = BranchRouteConfig {
            input_distribution: vec![6000, 3000], // 60% + 30% = 90% != 100%
            branch_routes: vec![
                vec![create_test_route(Dex::RaydiumCpmm, mint_a, mint_d, 0)],
                vec![create_test_route(Dex::Orca, mint_b, mint_d, 0)],
            ],
            target_mint: mint_d,
        };

        let result = BranchingRouteExecutor::validate_config(&branch_config);
        assert!(result.is_err(), "错误的分配比例应该验证失败");
    }
}

#[cfg(test)]
mod batch_route_tests {
    use super::*;

    #[test]
    fn test_batch_route_validation() {
        let (mint_a, mint_b, mint_c, mint_d) = create_test_mints();

        // 创建批量路由配置
        let batch_config = BatchRouteConfig {
            routes: vec![
                // 路由1: A -> B
                RouteConfig {
                    mode: RoutingMode::Linear,
                    routes: vec![create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0)],
                    amount_in: 50_000,
                    min_amount_out: 0,
                    flash_loan: None,
                    max_slippage_bps: 300,
                },
                // 路由2: C -> D
                RouteConfig {
                    mode: RoutingMode::Linear,
                    routes: vec![create_test_route(Dex::Orca, mint_c, mint_d, 0)],
                    amount_in: 30_000,
                    min_amount_out: 0,
                    flash_loan: None,
                    max_slippage_bps: 300,
                },
            ],
            atomic: true,
        };

        let result = BatchedRouteExecutor::validate_config(&batch_config);
        assert!(result.is_ok(), "批量路由配置验证应该成功");

        // 检查并行执行可行性
        let can_parallel = BatchedRouteExecutor::can_execute_parallel(&batch_config);
        assert!(can_parallel, "独立代币的批量路由应该可以并行执行");
    }

    #[test]
    fn test_batch_route_gas_estimation() {
        let (mint_a, mint_b, mint_c, mint_d) = create_test_mints();

        let batch_config = BatchRouteConfig {
            routes: vec![
                RouteConfig {
                    mode: RoutingMode::Circular,
                    routes: vec![
                        create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
                        create_test_route(Dex::Orca, mint_b, mint_a, 0),
                    ],
                    amount_in: 100_000,
                    min_amount_out: 0,
                    flash_loan: None,
                    max_slippage_bps: 500,
                },
                RouteConfig {
                    mode: RoutingMode::Linear,
                    routes: vec![
                        create_test_route(Dex::MeteoraAmm, mint_c, mint_d, 0),
                    ],
                    amount_in: 50_000,
                    min_amount_out: 0,
                    flash_loan: None,
                    max_slippage_bps: 300,
                },
            ],
            atomic: false,
        };

        let gas_cost = BatchedRouteExecutor::estimate_gas_cost(&batch_config);
        assert!(gas_cost > 0, "批量路由Gas估算应该大于0");
        assert!(gas_cost < 1_400_000, "批量路由Gas应该在Solana限制内");
    }

    #[test]
    fn test_batch_route_feasibility() {
        let (mint_a, mint_b, _, _) = create_test_mints();

        let batch_config = BatchRouteConfig {
            routes: vec![
                RouteConfig {
                    mode: RoutingMode::Linear,
                    routes: vec![create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0)],
                    amount_in: 100_000,
                    min_amount_out: 0,
                    flash_loan: None,
                    max_slippage_bps: 300,
                },
            ],
            atomic: true,
        };

        let result = BatchedRouteExecutor::validate_feasibility(&batch_config);
        assert!(result.is_ok(), "批量路由可行性验证应该成功");
    }

    #[test]
    fn test_conflicting_batch_routes() {
        let (mint_a, mint_b, _, _) = create_test_mints();

        // 创建有冲突的批量路由（使用相同的代币）
        let batch_config = BatchRouteConfig {
            routes: vec![
                RouteConfig {
                    mode: RoutingMode::Linear,
                    routes: vec![create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0)],
                    amount_in: 50_000,
                    min_amount_out: 0,
                    flash_loan: None,
                    max_slippage_bps: 300,
                },
                RouteConfig {
                    mode: RoutingMode::Linear,
                    routes: vec![create_test_route(Dex::Orca, mint_a, mint_b, 0)], // 冲突！
                    amount_in: 30_000,
                    min_amount_out: 0,
                    flash_loan: None,
                    max_slippage_bps: 300,
                },
            ],
            atomic: true,
        };

        let can_parallel = BatchedRouteExecutor::can_execute_parallel(&batch_config);
        assert!(!can_parallel, "冲突的批量路由不应该可以并行执行");
    }
}

#[cfg(test)]
mod multi_hop_executor_tests {
    use super::*;

    #[test]
    fn test_route_sequence_validation() {
        let (mint_a, mint_b, mint_c, _) = create_test_mints();

        let routes = vec![
            create_test_route(Dex::RaydiumClmm, mint_a, mint_b, 0),
            create_test_route(Dex::MeteoraLb, mint_b, mint_c, 0),
        ];

        let result = MultiHopRouteExecutor::validate_route_liquidity(&routes, 100_000);
        assert!(result.is_ok(), "路由序列流动性验证应该成功");
    }

    #[test]
    fn test_output_estimation() {
        let (mint_a, mint_b, mint_c, _) = create_test_mints();

        let routes = vec![
            create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
            create_test_route(Dex::Orca, mint_b, mint_c, 0),
        ];

        let output = MultiHopRouteExecutor::estimate_total_output(&routes, 100_000);
        assert!(output.is_ok(), "输出估算应该成功");

        let estimated = output.unwrap();
        assert!(estimated > 0, "估算输出应该大于0");
        assert!(estimated < 100_000, "考虑费用，估算输出应该小于输入");
    }

    #[test]
    fn test_complexity_scoring() {
        let (mint_a, mint_b, mint_c, mint_d) = create_test_mints();

        // 简单路由
        let simple_routes = vec![
            create_test_route(Dex::PumpSwap, mint_a, mint_b, 0),
        ];

        // 复杂路由
        let complex_routes = vec![
            create_test_route(Dex::RaydiumClmm, mint_a, mint_b, 0),
            create_test_route(Dex::MeteoraLb, mint_b, mint_c, 0),
            create_test_route(Dex::Orca, mint_c, mint_d, 0),
        ];

        let simple_score = MultiHopRouteExecutor::get_complexity_score(&simple_routes);
        let complex_score = MultiHopRouteExecutor::get_complexity_score(&complex_routes);

        assert!(complex_score > simple_score, "复杂路由的评分应该更高");
    }
}

#[cfg(test)]
mod integration_tests {
    use super::*;

    #[test]
    fn test_end_to_end_linear_route() {
        let (mint_a, mint_b, mint_c, _) = create_test_mints();

        // 1. 创建线性路由配置
        let route_config = RouteConfig {
            mode: RoutingMode::Linear,
            routes: vec![
                create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
                create_test_route(Dex::Orca, mint_b, mint_c, 0),
            ],
            amount_in: 100_000,
            min_amount_out: 80_000,
            flash_loan: None,
            max_slippage_bps: 300,
        };

        // 2. 验证配置
        assert!(route_config.validate().is_ok(), "路由配置应该有效");

        // 3. 估算Gas消耗
        let gas_cost = LinearRouteExecutor::estimate_gas_cost(&route_config.routes);
        assert!(gas_cost > 0 && gas_cost < 1_000_000, "Gas估算应该合理");

        // 4. 验证滑点容忍度
        let slippage_result = LinearRouteExecutor::validate_slippage_tolerance(
            &route_config.routes,
            route_config.amount_in,
            route_config.max_slippage_bps,
        );
        assert!(slippage_result.is_ok(), "滑点验证应该通过");
    }

    #[test]
    fn test_end_to_end_circular_arbitrage() {
        let (mint_a, mint_b, mint_c, _) = create_test_mints();

        // 1. 创建套利路由
        let routes = vec![
            create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
            create_test_route(Dex::Orca, mint_b, mint_c, 0),
            create_test_route(Dex::MeteoraAmm, mint_c, mint_a, 0), // 回到起始代币
        ];

        // 2. 验证循环路由
        assert!(CircularRouteExecutor::validate_liquidity(&routes, 100_000).is_ok());

        // 3. 估算利润
        let profit = CircularRouteExecutor::estimate_profit(&routes, 100_000, None);
        assert!(profit.is_ok(), "利润估算应该成功");

        // 4. 检查套利可行性
        let is_profitable = CircularRouteExecutor::is_profitable(
            &routes,
            100_000,
            1000,
            None,
        );
        assert!(is_profitable.is_ok(), "套利可行性检查应该成功");
    }

    #[test]
    fn test_comprehensive_route_validation() {
        let (mint_a, mint_b, mint_c, mint_d) = create_test_mints();

        // 测试所有路由模式的基本功能

        // 1. 线性路由
        let linear_config = RouteConfig {
            mode: RoutingMode::Linear,
            routes: vec![
                create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
                create_test_route(Dex::Orca, mint_b, mint_c, 0),
            ],
            amount_in: 100_000,
            min_amount_out: 80_000,
            flash_loan: None,
            max_slippage_bps: 300,
        };
        assert!(linear_config.validate().is_ok());

        // 2. 循环路由
        let circular_config = RouteConfig {
            mode: RoutingMode::Circular,
            routes: vec![
                create_test_route(Dex::RaydiumCpmm, mint_a, mint_b, 0),
                create_test_route(Dex::Orca, mint_b, mint_c, 0),
                create_test_route(Dex::MeteoraAmm, mint_c, mint_a, 0),
            ],
            amount_in: 100_000,
            min_amount_out: 100_000, // 套利至少要盈亏平衡
            flash_loan: None,
            max_slippage_bps: 500,
        };
        assert!(circular_config.validate().is_ok());

        // 3. 分支路由
        let branch_config = BranchRouteConfig {
            input_distribution: vec![7000, 3000],
            branch_routes: vec![
                vec![create_test_route(Dex::RaydiumCpmm, mint_a, mint_d, 0)],
                vec![create_test_route(Dex::Orca, mint_a, mint_d, 0)],
            ],
            target_mint: mint_d,
        };
        assert!(BranchingRouteExecutor::validate_config(&branch_config).is_ok());

        // 4. 批量路由
        let batch_config = BatchRouteConfig {
            routes: vec![linear_config, circular_config],
            atomic: false,
        };
        assert!(BatchedRouteExecutor::validate_config(&batch_config).is_ok());
    }
}

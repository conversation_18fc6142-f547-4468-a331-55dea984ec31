//! 集成测试
//! 
//! 测试onchain-router的核心功能

use onchain_router::routing::*;
use onchain_router::utils::*;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_route_config_validation() {
        // 测试有效的线性路由配置
        let valid_config = RouteConfig {
            mode: RoutingMode::Linear,
            routes: vec![
                Route {
                    dex: Dex::RaydiumClmm,
                    input_mint: solana_sdk::pubkey!("So11111111111111111111111111111111111111112"), // SOL
                    output_mint: solana_sdk::pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), // USDC
                    swap_data: vec![1, 2, 3, 4],
                    min_amount_out: 100,
                },
            ],
            amount_in: 1000,
            min_amount_out: 100,
            flash_loan: None,
            max_slippage_bps: 300,
        };
        
        assert!(valid_config.validate().is_ok());
    }

    #[test]
    fn test_circular_route_validation() {
        let sol_mint = solana_sdk::pubkey!("So11111111111111111111111111111111111111112");
        let usdc_mint = solana_sdk::pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");
        
        // 测试有效的循环路由（套利）
        let circular_config = RouteConfig {
            mode: RoutingMode::Circular,
            routes: vec![
                Route {
                    dex: Dex::RaydiumClmm,
                    input_mint: usdc_mint, // 开始：USDC
                    output_mint: sol_mint,  // 第一步：USDC -> SOL
                    swap_data: vec![1, 2, 3, 4],
                    min_amount_out: 100,
                },
                Route {
                    dex: Dex::MeteoraLb,
                    input_mint: sol_mint,   // 第二步：SOL -> USDC
                    output_mint: usdc_mint, // 结束：回到USDC
                    swap_data: vec![5, 6, 7, 8],
                    min_amount_out: 105, // 期望5%利润
                },
            ],
            amount_in: 1000,
            min_amount_out: 1005, // 最小0.5%利润
            flash_loan: None,
            max_slippage_bps: 300,
        };
        
        assert!(circular_config.validate().is_ok());
    }

    #[test]
    fn test_invalid_circular_route() {
        let sol_mint = solana_sdk::pubkey!("So11111111111111111111111111111111111111112");
        let usdc_mint = solana_sdk::pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");
        let ray_mint = solana_sdk::pubkey!("4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R");
        
        // 测试无效的循环路由（起始和结束代币不匹配）
        let invalid_circular = RouteConfig {
            mode: RoutingMode::Circular,
            routes: vec![
                Route {
                    dex: Dex::RaydiumClmm,
                    input_mint: usdc_mint, // 开始：USDC
                    output_mint: sol_mint,
                    swap_data: vec![1, 2, 3, 4],
                    min_amount_out: 100,
                },
                Route {
                    dex: Dex::MeteoraLb,
                    input_mint: sol_mint,
                    output_mint: ray_mint, // 结束：RAY（不匹配）
                    swap_data: vec![5, 6, 7, 8],
                    min_amount_out: 100,
                },
            ],
            amount_in: 1000,
            min_amount_out: 1000,
            flash_loan: None,
            max_slippage_bps: 300,
        };
        
        assert!(invalid_circular.validate().is_err());
    }

    #[test]
    fn test_route_discontinuity() {
        let sol_mint = solana_sdk::pubkey!("So11111111111111111111111111111111111111112");
        let usdc_mint = solana_sdk::pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");
        let ray_mint = solana_sdk::pubkey!("4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R");
        
        // 测试路由不连续（第一步输出与第二步输入不匹配）
        let discontinuous_config = RouteConfig {
            mode: RoutingMode::Linear,
            routes: vec![
                Route {
                    dex: Dex::RaydiumClmm,
                    input_mint: usdc_mint,
                    output_mint: sol_mint, // 输出SOL
                    swap_data: vec![1, 2, 3, 4],
                    min_amount_out: 100,
                },
                Route {
                    dex: Dex::MeteoraLb,
                    input_mint: ray_mint, // 输入RAY（不连续！）
                    output_mint: usdc_mint,
                    swap_data: vec![5, 6, 7, 8],
                    min_amount_out: 100,
                },
            ],
            amount_in: 1000,
            min_amount_out: 100,
            flash_loan: None,
            max_slippage_bps: 300,
        };
        
        assert!(discontinuous_config.validate().is_err());
    }

    #[test]
    fn test_math_utilities() {
        // 测试安全数学运算
        assert_eq!(safe_mul(100, 200).unwrap(), 20000);
        assert_eq!(safe_div(1000, 10).unwrap(), 100);
        assert_eq!(safe_add(100, 200).unwrap(), 300);
        assert_eq!(safe_sub(200, 100).unwrap(), 100);
        
        // 测试基点计算
        assert_eq!(calculate_bps(10000, 300).unwrap(), 300); // 3%
        assert_eq!(calculate_bps(10000, 100).unwrap(), 100); // 1%
        
        // 测试滑点计算
        assert_eq!(calculate_slippage_bps(1000, 950).unwrap(), 500); // 5%滑点
        assert_eq!(calculate_slippage_bps(1000, 1050).unwrap(), 0);  // 正滑点
    }

    #[test]
    fn test_route_complexity_calculation() {
        let routes = vec![
            Route {
                dex: Dex::RaydiumClmm, // 复杂度权重3
                input_mint: solana_sdk::pubkey!("So11111111111111111111111111111111111111112"),
                output_mint: solana_sdk::pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"),
                swap_data: vec![],
                min_amount_out: 0,
            },
            Route {
                dex: Dex::MeteoraLb, // 复杂度权重3
                input_mint: solana_sdk::pubkey!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"),
                output_mint: solana_sdk::pubkey!("4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R"),
                swap_data: vec![],
                min_amount_out: 0,
            },
        ];
        
        let complexity = calculate_route_complexity(&routes);
        // 基础分数(2) + 多样性分数(2) + DEX复杂度分数(6) = 10
        assert_eq!(complexity, 10);
    }

    #[test] 
    fn test_flash_loan_config_validation() {
        let flash_config = FlashLoanConfig {
            provider: solana_sdk::pubkey!("So11111111111111111111111111111111111111112"),
            amount: 1_000_000,
            max_fee_bps: 10, // 0.1%
            collateral_mint: None,
        };
        
        // 基本配置应该有效
        assert!(flash_config.amount > 0);
        assert!(flash_config.max_fee_bps <= 1000); // 合理的费率上限
    }
}
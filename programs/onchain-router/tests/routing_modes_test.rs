//! 路由模式集成测试
//!
//! 测试所有四种路由模式的基本功能

use anchor_lang::prelude::*;
use anchor_lang::InstructionData;
use onchain_router::routing::types::*;
use solana_program_test::*;
use solana_sdk::{
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    transaction::Transaction,
};

#[tokio::test]
async fn test_linear_route_mode() {
    let program_id = Pubkey::new_unique();
    let mut program_test = ProgramTest::new(
        "onchain_router",
        program_id,
        processor!(onchain_router::entry),
    );

    let (mut banks_client, payer, recent_blockhash) = program_test.start().await;

    // 创建线性路由配置
    let linear_route_config = RouteConfig {
        mode: RoutingMode::Linear,
        routes: vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![1, 2, 3, 4], // 模拟交换数据
                min_amount_out: 1000,
            },
            Route {
                dex: Dex::Orca,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![5, 6, 7, 8],
                min_amount_out: 2000,
            },
        ],
        amount_in: 10000,
        min_amount_out: 9000,
        flash_loan: None,
        max_slippage_bps: 300,
    };

    // 验证线性路由配置
    assert!(linear_route_config.validate().is_ok());
    assert_eq!(linear_route_config.mode, RoutingMode::Linear);
    assert_eq!(linear_route_config.routes.len(), 2);
    assert_eq!(linear_route_config.complexity_score(), 2); // 2 steps * 1 (linear multiplier)
}

#[tokio::test]
async fn test_circular_route_mode() {
    let program_id = Pubkey::new_unique();
    let mut program_test = ProgramTest::new(
        "onchain_router",
        program_id,
        processor!(onchain_router::entry),
    );

    let (mut banks_client, payer, recent_blockhash) = program_test.start().await;

    let start_mint = Pubkey::new_unique();

    // 创建循环路由配置（套利）
    let circular_route_config = RouteConfig {
        mode: RoutingMode::Circular,
        routes: vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: start_mint,
                output_mint: Pubkey::new_unique(),
                swap_data: vec![1, 2, 3, 4],
                min_amount_out: 1000,
            },
            Route {
                dex: Dex::MeteoraAmm,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![5, 6, 7, 8],
                min_amount_out: 2000,
            },
            Route {
                dex: Dex::Orca,
                input_mint: Pubkey::new_unique(),
                output_mint: start_mint, // 回到起始代币
                swap_data: vec![9, 10, 11, 12],
                min_amount_out: 10500, // 期望套利利润
            },
        ],
        amount_in: 10000,
        min_amount_out: 10100, // 期望至少1%利润
        flash_loan: None,
        max_slippage_bps: 200,
    };

    // 验证循环路由配置
    assert!(circular_route_config.validate().is_ok());
    assert_eq!(circular_route_config.mode, RoutingMode::Circular);
    assert_eq!(circular_route_config.routes.len(), 3);
    assert_eq!(circular_route_config.complexity_score(), 6); // 3 steps * 2 (circular multiplier)
}

#[tokio::test]
async fn test_branch_route_mode() {
    let program_id = Pubkey::new_unique();
    let mut program_test = ProgramTest::new(
        "onchain_router",
        program_id,
        processor!(onchain_router::entry),
    );

    let (mut banks_client, payer, recent_blockhash) = program_test.start().await;

    let target_mint = Pubkey::new_unique();

    // 创建分支路由配置
    let branch_route_config = BranchRouteConfig {
        input_distribution: vec![5000, 3000, 2000], // 50%, 30%, 20%
        branch_routes: vec![
            // 分支1: 50%的输入
            vec![Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::new_unique(),
                output_mint: target_mint,
                swap_data: vec![1, 2, 3, 4],
                min_amount_out: 5000,
            }],
            // 分支2: 30%的输入
            vec![
                Route {
                    dex: Dex::MeteoraAmm,
                    input_mint: Pubkey::new_unique(),
                    output_mint: Pubkey::new_unique(),
                    swap_data: vec![5, 6, 7, 8],
                    min_amount_out: 3000,
                },
                Route {
                    dex: Dex::Orca,
                    input_mint: Pubkey::new_unique(),
                    output_mint: target_mint,
                    swap_data: vec![9, 10, 11, 12],
                    min_amount_out: 2900,
                },
            ],
            // 分支3: 20%的输入
            vec![Route {
                dex: Dex::PumpSwap,
                input_mint: Pubkey::new_unique(),
                output_mint: target_mint,
                swap_data: vec![13, 14, 15, 16],
                min_amount_out: 2000,
            }],
        ],
        target_mint,
    };

    // 验证分支路由配置
    let total_distribution: u32 = branch_route_config.input_distribution.iter().map(|&x| x as u32).sum();
    assert_eq!(total_distribution, 10000); // 100%
    assert_eq!(branch_route_config.branch_routes.len(), 3);
    assert_eq!(branch_route_config.input_distribution.len(), branch_route_config.branch_routes.len());

    // 验证所有分支最终都输出到目标代币
    for branch_routes in &branch_route_config.branch_routes {
        if let Some(last_route) = branch_routes.last() {
            assert_eq!(last_route.output_mint, target_mint);
        }
    }
}

#[tokio::test]
async fn test_batch_route_mode() {
    let program_id = Pubkey::new_unique();
    let mut program_test = ProgramTest::new(
        "onchain_router",
        program_id,
        processor!(onchain_router::entry),
    );

    let (mut banks_client, payer, recent_blockhash) = program_test.start().await;

    // 创建批量路由配置
    let batch_route_config = BatchRouteConfig {
        routes: vec![
            // 路由1: 简单的代币交换
            RouteConfig {
                mode: RoutingMode::Linear,
                routes: vec![Route {
                    dex: Dex::RaydiumClmm,
                    input_mint: Pubkey::new_unique(),
                    output_mint: Pubkey::new_unique(),
                    swap_data: vec![1, 2, 3, 4],
                    min_amount_out: 1000,
                }],
                amount_in: 5000,
                min_amount_out: 4500,
                flash_loan: None,
                max_slippage_bps: 300,
            },
            // 路由2: 多跳交换
            RouteConfig {
                mode: RoutingMode::Linear,
                routes: vec![
                    Route {
                        dex: Dex::MeteoraAmm,
                        input_mint: Pubkey::new_unique(),
                        output_mint: Pubkey::new_unique(),
                        swap_data: vec![5, 6, 7, 8],
                        min_amount_out: 2000,
                    },
                    Route {
                        dex: Dex::Orca,
                        input_mint: Pubkey::new_unique(),
                        output_mint: Pubkey::new_unique(),
                        swap_data: vec![9, 10, 11, 12],
                        min_amount_out: 1800,
                    },
                ],
                amount_in: 3000,
                min_amount_out: 2700,
                flash_loan: None,
                max_slippage_bps: 250,
            },
        ],
        atomic: true, // 要求原子性执行
    };

    // 验证批量路由配置
    assert!(!batch_route_config.routes.is_empty());
    assert_eq!(batch_route_config.routes.len(), 2);
    assert!(batch_route_config.atomic);

    // 验证每个路由配置的有效性
    for route_config in &batch_route_config.routes {
        assert!(route_config.validate().is_ok());
    }

    // 计算总复杂度
    let total_complexity: u32 = batch_route_config.routes.iter()
        .map(|route| route.complexity_score() as u32)
        .sum();
    assert!(total_complexity <= 100); // 验证复杂度限制
}

#[tokio::test]
async fn test_flash_loan_route_configuration() {
    let program_id = Pubkey::new_unique();
    let mut program_test = ProgramTest::new(
        "onchain_router",
        program_id,
        processor!(onchain_router::entry),
    );

    let (mut banks_client, payer, recent_blockhash) = program_test.start().await;

    // 创建闪电贷路由配置
    let flash_loan_config = FlashLoanConfig {
        provider: Pubkey::new_unique(),
        amount: 100000,
        max_fee_bps: 30, // 0.3%费率
        collateral_mint: None,
    };

    let flash_loan_route_config = FlashLoanRouteConfig {
        flash_loan: flash_loan_config,
        arbitrage_routes: vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![1, 2, 3, 4],
                min_amount_out: 101000,
            },
            Route {
                dex: Dex::Orca,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![5, 6, 7, 8],
                min_amount_out: 102000,
            },
        ],
        expected_profit: 2000, // 期望2%利润
        max_gas_fee: 5000,
    };

    // 验证闪电贷配置
    assert_eq!(flash_loan_route_config.flash_loan.amount, 100000);
    assert_eq!(flash_loan_route_config.flash_loan.max_fee_bps, 30);
    assert_eq!(flash_loan_route_config.arbitrage_routes.len(), 2);
    assert_eq!(flash_loan_route_config.expected_profit, 2000);
    assert_eq!(flash_loan_route_config.max_gas_fee, 5000);

    // 验证套利路由
    for route in &flash_loan_route_config.arbitrage_routes {
        assert!(route.validate().is_ok());
    }
}

#[tokio::test]
async fn test_route_validation_rules() {
    // 测试路由验证规则

    // 1. 测试空路由路径
    let empty_route_config = RouteConfig {
        mode: RoutingMode::Linear,
        routes: vec![],
        amount_in: 1000,
        min_amount_out: 900,
        flash_loan: None,
        max_slippage_bps: 300,
    };
    assert!(empty_route_config.validate().is_err());

    // 2. 测试路由路径过长
    let long_route_config = RouteConfig {
        mode: RoutingMode::Linear,
        routes: vec![Route {
            dex: Dex::RaydiumClmm,
            input_mint: Pubkey::new_unique(),
            output_mint: Pubkey::new_unique(),
            swap_data: vec![1, 2, 3, 4],
            min_amount_out: 1000,
        }; 7], // 超过6步限制
        amount_in: 1000,
        min_amount_out: 900,
        flash_loan: None,
        max_slippage_bps: 300,
    };
    assert!(long_route_config.validate().is_err());

    // 3. 测试循环路由验证
    let invalid_circular_config = RouteConfig {
        mode: RoutingMode::Circular,
        routes: vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![1, 2, 3, 4],
                min_amount_out: 1000,
            },
            Route {
                dex: Dex::Orca,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(), // 不是循环
                swap_data: vec![5, 6, 7, 8],
                min_amount_out: 1000,
            },
        ],
        amount_in: 1000,
        min_amount_out: 900,
        flash_loan: None,
        max_slippage_bps: 300,
    };
    assert!(invalid_circular_config.validate().is_err());
}

#[test]
fn test_complexity_score_calculation() {
    // 测试复杂度评分计算

    let linear_route = RouteConfig {
        mode: RoutingMode::Linear,
        routes: vec![Route {
            dex: Dex::RaydiumClmm,
            input_mint: Pubkey::new_unique(),
            output_mint: Pubkey::new_unique(),
            swap_data: vec![1, 2, 3, 4],
            min_amount_out: 1000,
        }; 3],
        amount_in: 1000,
        min_amount_out: 900,
        flash_loan: None,
        max_slippage_bps: 300,
    };
    assert_eq!(linear_route.complexity_score(), 3); // 3 steps * 1

    let circular_route = RouteConfig {
        mode: RoutingMode::Circular,
        routes: vec![Route {
            dex: Dex::RaydiumClmm,
            input_mint: Pubkey::new_unique(),
            output_mint: Pubkey::new_unique(),
            swap_data: vec![1, 2, 3, 4],
            min_amount_out: 1000,
        }; 2],
        amount_in: 1000,
        min_amount_out: 900,
        flash_loan: None,
        max_slippage_bps: 300,
    };
    assert_eq!(circular_route.complexity_score(), 4); // 2 steps * 2

    let branching_route = RouteConfig {
        mode: RoutingMode::Branching,
        routes: vec![Route {
            dex: Dex::RaydiumClmm,
            input_mint: Pubkey::new_unique(),
            output_mint: Pubkey::new_unique(),
            swap_data: vec![1, 2, 3, 4],
            min_amount_out: 1000,
        }; 4],
        amount_in: 1000,
        min_amount_out: 900,
        flash_loan: None,
        max_slippage_bps: 300,
    };
    assert_eq!(branching_route.complexity_score(), 12); // 4 steps * 3

    let batched_route = RouteConfig {
        mode: RoutingMode::Batched,
        routes: vec![Route {
            dex: Dex::RaydiumClmm,
            input_mint: Pubkey::new_unique(),
            output_mint: Pubkey::new_unique(),
            swap_data: vec![1, 2, 3, 4],
            min_amount_out: 1000,
        }; 2],
        amount_in: 1000,
        min_amount_out: 900,
        flash_loan: None,
        max_slippage_bps: 300,
    };
    assert_eq!(batched_route.complexity_score(), 8); // 2 steps * 4
}

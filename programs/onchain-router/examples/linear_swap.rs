//! 线性交换示例
//! 
//! 演示如何使用线性路由进行多跳交换

use onchain_router::routing::*;

fn main() {
    println!("线性交换示例");
    
    // 创建一个 USDC -> SOL -> RAY 的线性路由
    let route_config = RouteConfig {
        mode: RoutingMode::Linear,
        routes: vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: "USDC_MINT".parse().unwrap(),
                output_mint: "SOL_MINT".parse().unwrap(),
                swap_data: vec![0; 32], // 模拟交换数据
                min_amount_out: 95_000, // 5%滑点保护
            },
            Route {
                dex: Dex::MeteoraLb,
                input_mint: "SOL_MINT".parse().unwrap(),
                output_mint: "RAY_MINT".parse().unwrap(),
                swap_data: vec![0; 32],
                min_amount_out: 90_000, // 总计约10%滑点
            },
        ],
        amount_in: 100_000, // 100 USDC
        min_amount_out: 90_000, // 最小90 RAY
        flash_loan: None,
        max_slippage_bps: 1000, // 10%
    };
    
    // 验证路由配置
    match route_config.validate() {
        Ok(()) => println!("✓ 路由配置验证通过"),
        Err(e) => println!("✗ 路由配置验证失败: {:?}", e),
    }
    
    // 计算复杂度分数
    let complexity = route_config.complexity_score();
    println!("路由复杂度分数: {}", complexity);
    
    println!("线性交换示例完成");
}
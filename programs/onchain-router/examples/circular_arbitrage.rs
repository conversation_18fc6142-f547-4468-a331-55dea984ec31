//! 循环套利示例
//!
//! 演示如何配置和执行循环路由（套利）

use onchain_router::routing::*;

fn main() {
    println!("循环套利示例");

    // 创建一个 USDC -> SOL -> RAY -> USDC 的套利路由
    let arbitrage_config = RouteConfig {
        mode: RoutingMode::Circular,
        routes: vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: "USDC_MINT".parse().unwrap(),
                output_mint: "SOL_MINT".parse().unwrap(),
                swap_data: vec![0; 32],
                min_amount_out: 950, // 相当于$95的SOL
            },
            Route {
                dex: Dex::MeteoraLb,
                input_mint: "SOL_MINT".parse().unwrap(),
                output_mint: "RAY_MINT".parse().unwrap(),
                swap_data: vec![0; 32],
                min_amount_out: 1900, // 相当于1900个RAY
            },
            Route {
                dex: Dex::Orca,
                input_mint: "RAY_MINT".parse().unwrap(),
                output_mint: "USDC_MINT".parse().unwrap(),
                swap_data: vec![0; 32],
                min_amount_out: 101_000, // 期望获得101 USDC（1%利润）
            },
        ],
        amount_in: 100_000, // 100 USDC
        min_amount_out: 100_500, // 最小0.5%利润
        flash_loan: None, // 不使用闪电贷
        max_slippage_bps: 500, // 5%滑点
    };

    // 验证循环路由
    match arbitrage_config.validate() {
        Ok(()) => println!("✓ 套利路由配置验证通过"),
        Err(e) => println!("✗ 套利路由配置验证失败: {:?}", e),
    }

    // 创建闪电贷套利配置
    let flash_arbitrage_config = FlashLoanRouteConfig {
        flash_loan: FlashLoanConfig {
            provider: "SOLEND_PROVIDER".parse().unwrap(),
            amount: 1_000_000, // 1M USDC闪电贷
            max_fee_bps: 5, // 最大0.05%费率
            collateral_mint: None,
        },
        arbitrage_routes: arbitrage_config.routes.clone(),
        expected_profit: 5_000, // 预期5 USDC利润
        max_gas_fee: 500, // 最大0.5 USDC Gas费
    };

    println!("配置完成:");
    println!("- 常规套利: 投入 {}，期望最小回报 {}",
        arbitrage_config.amount_in, arbitrage_config.min_amount_out);
    println!("- 闪电贷套利: 贷款 {}，预期利润 {}",
        flash_arbitrage_config.flash_loan.amount, flash_arbitrage_config.expected_profit);

    println!("循环套利示例完成");
}

[package]
name = "onchain-router"
version = "0.1.0"
description = "通用多跳路由智能合约 - 支持线性、循环、分支等路由模式"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "onchain_router"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []
idl-build = ["anchor-lang/idl-build", "anchor-spl/idl-build"]

[dependencies]
# Anchor framework
anchor-lang = { version = "0.31.1", features = ["init-if-needed", "event-cpi"]}
anchor-spl = "0.31.1"

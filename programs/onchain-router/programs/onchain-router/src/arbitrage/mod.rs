//! 套利系统
//! 
//! 提供完整的零本金闪电贷套利功能，包括策略管理、利润计算、风险控制和利润分配

pub mod strategy;
pub mod calculator;
pub mod risk_manager;
pub mod profit_distributor;

pub use strategy::*;
pub use calculator::*;
pub use risk_manager::*;
pub use profit_distributor::*;

use anchor_lang::prelude::*;
use crate::routing::types::{Route, RouteResult};
use crate::flash_loan::FlashLoanProvider;
use crate::error::RouteError;

/// 套利执行配置
#[derive(Debug, Clone)]
pub struct ArbitrageConfig {
    /// 最小利润阈值
    pub min_profit_threshold: u64,
    /// 最大闪电贷金额
    pub max_flash_loan_amount: u64,
    /// 最大滑点容忍度（基点）
    pub max_slippage_bps: u16,
    /// 最大Gas费用
    pub max_gas_fee: u64,
    /// 是否启用动态风险评估
    pub enable_dynamic_risk: bool,
    /// 利润分配比例（协议费用基点）
    pub protocol_fee_bps: u16,
}

impl Default for ArbitrageConfig {
    fn default() -> Self {
        Self {
            min_profit_threshold: 1000, // 0.001 SOL
            max_flash_loan_amount: 1_000_000_000, // 1 SOL
            max_slippage_bps: 300, // 3%
            max_gas_fee: 50000, // 0.05 SOL
            enable_dynamic_risk: true,
            protocol_fee_bps: 50, // 0.5%
        }
    }
}

/// 套利路径信息
#[derive(Debug, Clone)]
pub struct ArbitragePath {
    /// 路由步骤
    pub routes: Vec<Route>,
    /// 预估利润
    pub estimated_profit: u64,
    /// 风险评分（0-100）
    pub risk_score: u8,
    /// 成功概率（0.0-1.0）
    pub success_probability: f64,
    /// 执行优先级（越高越优先）
    pub priority: u8,
}

/// 套利执行结果
#[derive(Debug, Clone)]
pub struct ArbitrageResult {
    /// 执行结果
    pub route_result: RouteResult,
    /// 闪电贷费用
    pub flash_loan_fee: u64,
    /// 协议费用
    pub protocol_fee: u64,
    /// 净利润
    pub net_profit: u64,
    /// 风险评估结果
    pub risk_assessment: RiskAssessment,
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
}

/// 链上套利路径（可序列化）
#[derive(AnchorSerialize, AnchorDeserialize, Debug, Clone)]
pub struct OnchainArbitragePath {
    /// 路由数据
    pub route_data: Vec<u8>,
    /// 预估利润
    pub estimated_profit: u64,
    /// 最大滑点
    pub max_slippage_bps: u16,
    /// 超时时间（秒）
    pub timeout_seconds: u32,
}

impl OnchainArbitragePath {
    /// 验证套利路径的有效性
    pub fn validate(&self) -> Result<()> {
        if self.route_data.is_empty() {
            return Err(RouteError::InvalidRouteConfig.into());
        }
        
        if self.max_slippage_bps > 1000 { // 最大10%滑点
            return Err(RouteError::SlippageExceeded.into());
        }
        
        if self.timeout_seconds == 0 || self.timeout_seconds > 300 { // 最大5分钟
            return Err(RouteError::InvalidTimeout.into());
        }
        
        Ok(())
    }
}

/// 套利服务主接口
pub trait ArbitrageService {
    /// 寻找套利机会
    fn find_arbitrage_opportunities(
        &self,
        base_token: &Pubkey,
        min_profit: u64,
        max_risk_score: u8,
    ) -> Result<Vec<ArbitragePath>>;
    
    /// 执行闪电贷套利
    fn execute_flash_loan_arbitrage(
        &mut self,
        path: &ArbitragePath,
        flash_amount: u64,
        flash_loan_provider: Box<dyn FlashLoanProvider>,
        accounts: &[AccountInfo],
    ) -> Result<ArbitrageResult>;
    
    /// 评估套利路径
    fn evaluate_arbitrage_path(
        &self,
        path: &ArbitragePath,
        current_market_conditions: &MarketConditions,
    ) -> Result<ArbitrageEvaluation>;
}

/// 市场条件
#[derive(Debug, Clone)]
pub struct MarketConditions {
    /// 网络拥堵程度（0-100）
    pub network_congestion: u8,
    /// 平均Gas价格
    pub avg_gas_price: u64,
    /// 市场波动性（0-100）
    pub volatility: u8,
    /// 流动性水平（0-100）
    pub liquidity_level: u8,
}

/// 套利评估结果
#[derive(Debug, Clone)]
pub struct ArbitrageEvaluation {
    /// 调整后的预估利润
    pub adjusted_profit: u64,
    /// 总体风险评分
    pub overall_risk_score: u8,
    /// 推荐执行
    pub recommended: bool,
    /// 评估备注
    pub notes: String,
}

/// 套利事件
#[event]
pub struct ArbitrageExecutedEvent {
    /// 用户
    pub user: Pubkey,
    /// 套利路径哈希
    pub path_hash: [u8; 32],
    /// 闪电贷金额
    pub flash_loan_amount: u64,
    /// 净利润
    pub net_profit: u64,
    /// 风险评分
    pub risk_score: u8,
    /// 执行时间
    pub execution_time_ms: u64,
    /// 时间戳
    pub timestamp: i64,
}

/// 套利失败事件
#[event]
pub struct ArbitrageFailedEvent {
    /// 用户
    pub user: Pubkey,
    /// 套利路径哈希
    pub path_hash: [u8; 32],
    /// 错误代码
    pub error_code: u32,
    /// 失败阶段
    pub failed_stage: ArbitrageStage,
    /// 时间戳
    pub timestamp: i64,
}

/// 套利执行阶段
#[derive(AnchorSerialize, AnchorDeserialize, Debug, Clone, Copy, PartialEq)]
pub enum ArbitrageStage {
    /// 初始化
    Initialization = 0,
    /// 风险评估
    RiskAssessment = 1,
    /// 闪电贷申请
    FlashLoanRequest = 2,
    /// 路由执行
    RouteExecution = 3,
    /// 利润计算
    ProfitCalculation = 4,
    /// 闪电贷偿还
    FlashLoanRepayment = 5,
    /// 利润分配
    ProfitDistribution = 6,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_arbitrage_config_default() {
        let config = ArbitrageConfig::default();
        assert_eq!(config.min_profit_threshold, 1000);
        assert_eq!(config.max_flash_loan_amount, 1_000_000_000);
        assert_eq!(config.max_slippage_bps, 300);
        assert!(config.enable_dynamic_risk);
    }

    #[test]
    fn test_onchain_arbitrage_path_validation() {
        let mut path = OnchainArbitragePath {
            route_data: vec![1, 2, 3],
            estimated_profit: 1000,
            max_slippage_bps: 100,
            timeout_seconds: 60,
        };
        
        assert!(path.validate().is_ok());
        
        // 测试无效滑点
        path.max_slippage_bps = 1500;
        assert!(path.validate().is_err());
        
        // 测试无效超时
        path.max_slippage_bps = 100;
        path.timeout_seconds = 0;
        assert!(path.validate().is_err());
    }
}
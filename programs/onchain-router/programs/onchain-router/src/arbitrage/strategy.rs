//! 套利策略引擎
//!
//! 负责寻找套利机会、评估策略可行性和管理套利执行

use std::collections::BTreeMap;
use anchor_lang::prelude::*;
use crate::routing::types::{Route, Dex};
use super::{ArbitragePath, ArbitrageConfig};

/// 套利策略类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum ArbitrageStrategy {
    /// 双向套利（A->B->A）
    TwoWay,
    /// 三向套利（A->B->C->A）
    ThreeWay,
    /// 多路径套利（A->B->C->D->A）
    MultiPath,
    /// 跨DEX套利
    CrossDex,
}

/// 套利策略引擎
pub struct StrategyEngine {
    /// 配置
    config: ArbitrageConfig,
    /// 支持的DEX列表
    supported_dexes: Vec<Dex>,
    /// 代币对流动性映射
    liquidity_map: BTreeMap<(Pubkey, Pubkey), u64>,
    /// 策略权重配置
    strategy_weights: BTreeMap<ArbitrageStrategy, f64>,
}

impl StrategyEngine {
    /// 创建新的策略引擎
    pub fn new(config: ArbitrageConfig) -> Self {
        let mut strategy_weights = BTreeMap::new();
        strategy_weights.insert(ArbitrageStrategy::TwoWay, 1.0);
        strategy_weights.insert(ArbitrageStrategy::ThreeWay, 0.8);
        strategy_weights.insert(ArbitrageStrategy::MultiPath, 0.6);
        strategy_weights.insert(ArbitrageStrategy::CrossDex, 1.2);

        Self {
            config,
            supported_dexes: vec![
                Dex::RaydiumClmm,
                Dex::Orca,
                Dex::MeteoraLb,
                Dex::PumpSwap,
            ],
            liquidity_map: BTreeMap::new(),
            strategy_weights,
        }
    }

    /// 寻找套利机会
    pub fn find_arbitrage_opportunities(
        &self,
        base_token: &Pubkey,
        intermediate_tokens: &[Pubkey],
        max_paths: usize,
    ) -> Result<Vec<ArbitragePath>> {
        msg!("开始寻找套利机会 - 基础代币: {}", base_token);

        let mut opportunities = Vec::new();

        // 1. 寻找双向套利机会
        let two_way_paths = self.find_two_way_arbitrage(base_token, intermediate_tokens)?;
        opportunities.extend(two_way_paths);

        // 2. 寻找三向套利机会
        let three_way_paths = self.find_three_way_arbitrage(base_token, intermediate_tokens)?;
        opportunities.extend(three_way_paths);

        // 3. 寻找多路径套利机会
        if intermediate_tokens.len() >= 3 {
            let multi_path_paths = self.find_multi_path_arbitrage(base_token, intermediate_tokens)?;
            opportunities.extend(multi_path_paths);
        }

        // 4. 按照利润和风险评分排序
        opportunities.sort_by(|a, b| {
            let a_score = self.calculate_opportunity_score(a);
            let b_score = self.calculate_opportunity_score(b);
            b_score.partial_cmp(&a_score).unwrap_or(std::cmp::Ordering::Equal)
        });

        // 5. 限制返回数量
        opportunities.truncate(max_paths);

        msg!("找到 {} 个套利机会", opportunities.len());
        Ok(opportunities)
    }

    /// 寻找双向套利机会（A->B->A）
    fn find_two_way_arbitrage(
        &self,
        base_token: &Pubkey,
        intermediate_tokens: &[Pubkey],
    ) -> Result<Vec<ArbitragePath>> {
        let mut paths = Vec::new();

        for intermediate in intermediate_tokens {
            for dex1 in &self.supported_dexes {
                for dex2 in &self.supported_dexes {
                    if dex1 == dex2 {
                        continue; // 跨DEX套利
                    }

                    // 创建 A->B->A 路径
                    let route1 = Route {
                        dex: *dex1,
                        input_mint: *base_token,
                        output_mint: *intermediate,
                        min_amount_out: 0,
                        swap_data: vec![],
                    };

                    let route2 = Route {
                        dex: *dex2,
                        input_mint: *intermediate,
                        output_mint: *base_token,
                        swap_data: vec![],
                        min_amount_out: 0,
                    };

                    let routes = vec![route1, route2];

                    // 评估路径可行性
                    if let Ok(path) = self.evaluate_arbitrage_path(&routes, ArbitrageStrategy::TwoWay) {
                        if path.estimated_profit >= self.config.min_profit_threshold {
                            paths.push(path);
                        }
                    }
                }
            }
        }

        Ok(paths)
    }

    /// 寻找三向套利机会（A->B->C->A）
    fn find_three_way_arbitrage(
        &self,
        base_token: &Pubkey,
        intermediate_tokens: &[Pubkey],
    ) -> Result<Vec<ArbitragePath>> {
        let mut paths = Vec::new();

        for (i, token_b) in intermediate_tokens.iter().enumerate() {
            for (j, token_c) in intermediate_tokens.iter().enumerate() {
                if i == j || token_b == token_c {
                    continue;
                }

                // 尝试不同的DEX组合
                for dex1 in &self.supported_dexes {
                    for dex2 in &self.supported_dexes {
                        for dex3 in &self.supported_dexes {
                            // 创建 A->B->C->A 路径
                            let routes = vec![
                                Route {
                                    dex: *dex1,
                                    input_mint: *base_token,
                                    output_mint: *token_b,
                                    min_amount_out: 0,
                                    swap_data: vec![],
                                },
                                Route {
                                    dex: *dex2,
                                    input_mint: *token_b,
                                    output_mint: *token_c,
                                    min_amount_out: 0,
                                    swap_data: vec![],
                                },
                                Route {
                                    dex: *dex3,
                                    input_mint: *token_c,
                                    output_mint: *base_token,
                                    swap_data: vec![],
                                    min_amount_out: 0,
                                },
                            ];

                            if let Ok(path) = self.evaluate_arbitrage_path(&routes, ArbitrageStrategy::ThreeWay) {
                                if path.estimated_profit >= self.config.min_profit_threshold {
                                    paths.push(path);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(paths)
    }

    /// 寻找多路径套利机会
    fn find_multi_path_arbitrage(
        &self,
        base_token: &Pubkey,
        intermediate_tokens: &[Pubkey],
    ) -> Result<Vec<ArbitragePath>> {
        let mut paths = Vec::new();

        // 生成4步套利路径（A->B->C->D->A）
        if intermediate_tokens.len() >= 3 {
            for combo in self.generate_token_combinations(intermediate_tokens, 3) {
                let routes = vec![
                    Route {
                        dex: Dex::RaydiumClmm,
                        input_mint: *base_token,
                        output_mint: combo[0],
                        swap_data: vec![],
                        min_amount_out: 0,
                    },
                    Route {
                        dex: Dex::Orca,
                        input_mint: combo[0],
                        output_mint: combo[1],
                        swap_data: vec![],
                        min_amount_out: 0,
                    },
                    Route {
                        dex: Dex::MeteoraLb,
                        input_mint: combo[1],
                        output_mint: combo[2],
                        swap_data: vec![],
                        min_amount_out: 0,
                    },
                    Route {
                        dex: Dex::PumpSwap,
                        input_mint: combo[2],
                        output_mint: *base_token,
                        swap_data: vec![],
                        min_amount_out: 0,
                    },
                ];

                if let Ok(path) = self.evaluate_arbitrage_path(&routes, ArbitrageStrategy::MultiPath) {
                    if path.estimated_profit >= self.config.min_profit_threshold {
                        paths.push(path);
                    }
                }
            }
        }

        Ok(paths)
    }

    /// 评估套利路径
    fn evaluate_arbitrage_path(
        &self,
        routes: &[Route],
        strategy: ArbitrageStrategy,
    ) -> Result<ArbitragePath> {
        // 1. 计算预估利润
        let estimated_profit = self.estimate_path_profit(routes)?;

        // 2. 计算风险评分
        let risk_score = self.calculate_risk_score(routes, strategy)?;

        // 3. 计算成功概率
        let success_probability = self.calculate_success_probability(routes, strategy)?;

        // 4. 计算优先级
        let priority = self.calculate_priority(estimated_profit, risk_score, strategy);

        Ok(ArbitragePath {
            routes: routes.to_vec(),
            estimated_profit,
            risk_score,
            success_probability,
            priority,
        })
    }

    /// 估算路径利润
    fn estimate_path_profit(&self, routes: &[Route]) -> Result<u64> {
        // 模拟执行计算利润
        let test_amount = 100_000_000; // 0.1 SOL作为测试金额

        let mut current_amount = test_amount;

        for route in routes {
            // 简化的价格计算（实际应该查询真实价格）
            let exchange_rate = self.get_mock_exchange_rate(&route.input_mint, &route.output_mint);
            let fee_bps = self.get_dex_fee_bps(&route.dex);

            // 扣除交易费用
            let fee = current_amount * fee_bps as u64 / 10000;
            let amount_after_fee = current_amount.saturating_sub(fee);

            // 应用汇率
            current_amount = (amount_after_fee as f64 * exchange_rate) as u64;
        }

        // 计算利润
        let profit = current_amount.saturating_sub(test_amount);

        // 按比例调整到1 SOL的基础
        let scaled_profit = profit * 10; // 假设1 SOL的利润是0.1 SOL的10倍

        Ok(scaled_profit)
    }

    /// 计算风险评分
    fn calculate_risk_score(&self, routes: &[Route], strategy: ArbitrageStrategy) -> Result<u8> {
        let mut risk_score = 0u8;

        // 1. 路径长度风险
        let path_risk = match routes.len() {
            2 => 10,
            3 => 25,
            4 => 40,
            _ => 60,
        };
        risk_score += path_risk;

        // 2. DEX多样性风险
        let mut dexes = [false; 6]; // 支持6种DEX类型
        for route in routes {
            let dex_index = match route.dex {
                Dex::RaydiumClmm => 0,
                Dex::RaydiumCpmm => 1,
                Dex::MeteoraLb => 2,
                Dex::MeteoraAmm => 3,
                Dex::Orca => 4,
                Dex::PumpSwap => 5,
            };
            dexes[dex_index] = true;
        }
        let unique_dexes = dexes.iter().filter(|&&x| x).count();

        let dex_risk = match unique_dexes {
            1 => 20, // 单一DEX风险较高
            2 => 10,
            3 => 5,
            _ => 0,
        };
        risk_score += dex_risk;

        // 3. 流动性风险
        let liquidity_risk = self.assess_liquidity_risk(routes);
        risk_score += liquidity_risk;

        // 4. 策略类型风险
        let strategy_risk = match strategy {
            ArbitrageStrategy::TwoWay => 5,
            ArbitrageStrategy::ThreeWay => 15,
            ArbitrageStrategy::MultiPath => 25,
            ArbitrageStrategy::CrossDex => 10,
        };
        risk_score += strategy_risk;

        Ok(risk_score.min(100))
    }

    /// 计算成功概率
    fn calculate_success_probability(&self, routes: &[Route], strategy: ArbitrageStrategy) -> Result<f64> {
        let base_probability = match strategy {
            ArbitrageStrategy::TwoWay => 0.85,
            ArbitrageStrategy::ThreeWay => 0.75,
            ArbitrageStrategy::MultiPath => 0.60,
            ArbitrageStrategy::CrossDex => 0.80,
        };

        // 根据路径复杂度调整
        let complexity_factor = 1.0 - (routes.len() as f64 - 2.0) * 0.05;

        // 根据流动性调整
        let liquidity_factor = self.get_average_liquidity_factor(routes);

        let final_probability = base_probability * complexity_factor * liquidity_factor;
        Ok(final_probability.max(0.1).min(1.0))
    }

    /// 计算优先级
    fn calculate_priority(&self, profit: u64, risk_score: u8, strategy: ArbitrageStrategy) -> u8 {
        let profit_score = (profit / 1000).min(100) as f64; // 利润评分
        let risk_penalty = risk_score as f64; // 风险惩罚
        let strategy_weight = self.strategy_weights.get(&strategy).unwrap_or(&1.0);

        let priority_score = (profit_score * strategy_weight - risk_penalty * 0.5).max(0.0);
        priority_score.min(255.0) as u8
    }

    /// 计算机会评分（用于排序）
    fn calculate_opportunity_score(&self, path: &ArbitragePath) -> f64 {
        let profit_weight = 0.4;
        let risk_weight = 0.3;
        let probability_weight = 0.3;

        let profit_score = (path.estimated_profit as f64) / 1000.0;
        let risk_score = 100.0 - (path.risk_score as f64);
        let probability_score = path.success_probability * 100.0;

        profit_score * profit_weight + risk_score * risk_weight + probability_score * probability_weight
    }

    /// 评估流动性风险
    fn assess_liquidity_risk(&self, routes: &[Route]) -> u8 {
        let mut total_risk = 0u8;

        for route in routes {
            let pair_liquidity = self.liquidity_map
                .get(&(route.input_mint, route.output_mint))
                .unwrap_or(&0);

            let risk = match *pair_liquidity {
                0..=1_000_000 => 30,      // 低流动性
                1_000_001..=10_000_000 => 15,  // 中等流动性
                10_000_001..=100_000_000 => 5, // 高流动性
                _ => 0,                         // 极高流动性
            };

            total_risk += risk;
        }

        (total_risk / routes.len() as u8).min(50)
    }

    /// 获取平均流动性因子
    fn get_average_liquidity_factor(&self, routes: &[Route]) -> f64 {
        let mut total_factor = 0.0;

        for route in routes {
            let pair_liquidity = self.liquidity_map
                .get(&(route.input_mint, route.output_mint))
                .unwrap_or(&0);

            let factor = match *pair_liquidity {
                0..=1_000_000 => 0.7,
                1_000_001..=10_000_000 => 0.85,
                10_000_001..=100_000_000 => 0.95,
                _ => 1.0,
            };

            total_factor += factor;
        }

        total_factor / routes.len() as f64
    }

    /// 获取模拟汇率（实际应该查询链上价格）
    fn get_mock_exchange_rate(&self, input_mint: &Pubkey, output_mint: &Pubkey) -> f64 {
        // 简化的模拟汇率，实际应该查询DEX价格
        if input_mint == output_mint {
            1.0
        } else {
            0.98  // 0.98-1.02的随机汇率
        }
    }

    /// 获取DEX费率
    fn get_dex_fee_bps(&self, dex: &Dex) -> u16 {
        match dex {
            Dex::RaydiumClmm => 25,  // 0.25%
            Dex::RaydiumCpmm => 30,  // 0.30%
            Dex::Orca => 30,         // 0.30%
            Dex::MeteoraLb => 15,    // 0.15%
            Dex::MeteoraAmm => 20,   // 0.20%
            Dex::PumpSwap => 100,    // 1.00%
        }
    }

    /// 生成代币组合
    fn generate_token_combinations(&self, tokens: &[Pubkey], size: usize) -> Vec<Vec<Pubkey>> {
        if size == 0 || size > tokens.len() {
            return vec![];
        }

        if size == 1 {
            return tokens.iter().map(|&t| vec![t]).collect();
        }

        let mut combinations = Vec::new();
        for i in 0..tokens.len() {
            let sub_combinations = self.generate_token_combinations(&tokens[i+1..], size - 1);
            for mut combo in sub_combinations {
                combo.insert(0, tokens[i]);
                combinations.push(combo);
            }
        }

        combinations
    }

    /// 更新流动性数据
    pub fn update_liquidity_data(&mut self, pair: (Pubkey, Pubkey), liquidity: u64) {
        self.liquidity_map.insert(pair, liquidity);
        // 同时更新反向交易对
        self.liquidity_map.insert((pair.1, pair.0), liquidity);
    }

    /// 批量更新流动性数据
    pub fn batch_update_liquidity(&mut self, liquidity_data: Vec<((Pubkey, Pubkey), u64)>) {
        for (pair, liquidity) in liquidity_data {
            self.update_liquidity_data(pair, liquidity);
        }
        msg!("已更新 {} 个交易对的流动性数据", self.liquidity_map.len() / 2);
    }

    /// 设置策略权重
    pub fn set_strategy_weight(&mut self, strategy: ArbitrageStrategy, weight: f64) {
        self.strategy_weights.insert(strategy, weight);
        msg!("策略权重已更新: {:?} = {:.2}", strategy, weight);
    }

    /// 获取策略统计信息
    pub fn get_strategy_stats(&self) -> StrategyStats {
        StrategyStats {
            total_pairs: self.liquidity_map.len() / 2,
            supported_dexes: self.supported_dexes.len(),
            strategy_weights: self.strategy_weights.clone(),
            high_liquidity_pairs: self.liquidity_map.values()
                .filter(|&&liquidity| liquidity > 10_000_000)
                .count(),
        }
    }
}

/// 策略统计信息
#[derive(Debug, Clone)]
pub struct StrategyStats {
    /// 总交易对数量
    pub total_pairs: usize,
    /// 支持的DEX数量
    pub supported_dexes: usize,
    /// 策略权重配置
    pub strategy_weights: BTreeMap<ArbitrageStrategy, f64>,
    /// 高流动性交易对数量
    pub high_liquidity_pairs: usize,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_strategy_engine_creation() {
        let config = ArbitrageConfig::default();
        let engine = StrategyEngine::new(config);

        assert_eq!(engine.supported_dexes.len(), 4);
        assert!(engine.strategy_weights.contains_key(&ArbitrageStrategy::TwoWay));
    }

}

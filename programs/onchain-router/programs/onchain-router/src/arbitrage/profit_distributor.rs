//! 利润分配系统
//! 
//! 负责套利利润的精确分配、转账执行和分配记录

use anchor_lang::prelude::*;
use anchor_spl::token::{self, Token, TokenAccount, Transfer};
use crate::error::RouteError;
use super::{ArbitrageConfig, ArbitrageResult};

/// 利润分配器
pub struct ProfitDistributor {
    /// 配置
    #[allow(dead_code)]
    config: ArbitrageConfig,
    /// 协议费率
    protocol_fee_bps: u16,
    /// 运营费率
    operator_fee_bps: u16,
    /// 开发者费率
    developer_fee_bps: u16,
}

/// 分配目标账户
#[derive(Clone)]
pub struct DistributionTargets<'info> {
    /// 用户利润账户
    pub user_account: &'info Account<'info, TokenAccount>,
    /// 协议费用账户
    pub protocol_account: &'info Account<'info, TokenAccount>,
    /// 运营费用账户
    pub operator_account: Option<&'info Account<'info, TokenAccount>>,
    /// 开发者费用账户
    pub developer_account: Option<&'info Account<'info, TokenAccount>>,
    /// Token程序
    pub token_program: &'info Program<'info, Token>,
}

/// 分配结果
#[derive(Debug, Clone)]
pub struct DistributionResult {
    /// 用户获得金额
    pub user_amount: u64,
    /// 协议费用金额
    pub protocol_amount: u64,
    /// 运营费用金额
    pub operator_amount: u64,
    /// 开发者费用金额
    pub developer_amount: u64,
    /// 总分配金额
    pub total_distributed: u64,
    /// 剩余金额（舍入误差）
    pub remaining_amount: u64,
}

/// 分配统计
#[derive(Debug, Clone, Default)]
pub struct DistributionStats {
    /// 总分配次数
    pub total_distributions: u64,
    /// 总分配金额
    pub total_amount_distributed: u64,
    /// 总用户利润
    pub total_user_profit: u64,
    /// 总协议费用
    pub total_protocol_fees: u64,
    /// 总运营费用
    pub total_operator_fees: u64,
    /// 平均分配金额
    pub average_distribution: u64,
}

impl ProfitDistributor {
    /// 创建新的利润分配器
    pub fn new(config: ArbitrageConfig) -> Self {
        Self {
            config,
            protocol_fee_bps: 30,   // 0.3%
            operator_fee_bps: 20,   // 0.2%
            developer_fee_bps: 10,  // 0.1%
        }
    }
    
    /// 执行利润分配
    pub fn distribute_profit<'info>(
        &self,
        arbitrage_result: &ArbitrageResult,
        source_account: &Account<'info, TokenAccount>,
        targets: &DistributionTargets<'info>,
        authority: &AccountInfo<'info>,
        authority_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<DistributionResult> {
        let net_profit = arbitrage_result.net_profit;
        
        if net_profit == 0 {
            msg!("无利润需要分配");
            return Ok(DistributionResult {
                user_amount: 0,
                protocol_amount: 0,
                operator_amount: 0,
                developer_amount: 0,
                total_distributed: 0,
                remaining_amount: 0,
            });
        }
        
        msg!("开始分配利润 - 总金额: {}", net_profit);
        
        // 1. 计算各方分配金额
        let distribution_amounts = self.calculate_distribution_amounts(net_profit)?;
        
        // 2. 验证分配账户
        self.validate_distribution_targets(targets)?;
        
        // 3. 执行转账
        self.execute_transfers(
            source_account,
            targets,
            authority,
            authority_seeds,
            &distribution_amounts,
        )?;
        
        // 4. 验证分配结果
        let total_distributed = distribution_amounts.user_amount 
            + distribution_amounts.protocol_amount
            + distribution_amounts.operator_amount
            + distribution_amounts.developer_amount;
        
        let remaining = net_profit.saturating_sub(total_distributed);
        
        let result = DistributionResult {
            user_amount: distribution_amounts.user_amount,
            protocol_amount: distribution_amounts.protocol_amount,
            operator_amount: distribution_amounts.operator_amount,
            developer_amount: distribution_amounts.developer_amount,
            total_distributed,
            remaining_amount: remaining,
        };
        
        msg!("利润分配完成 - 用户: {}, 协议: {}, 运营: {}, 开发者: {}, 剩余: {}", 
            result.user_amount, result.protocol_amount, result.operator_amount, 
            result.developer_amount, result.remaining_amount);
        
        Ok(result)
    }
    
    /// 计算分配金额
    fn calculate_distribution_amounts(&self, net_profit: u64) -> Result<DistributionAmounts> {
        // 1. 计算协议费用
        let protocol_amount = self.calculate_fee(net_profit, self.protocol_fee_bps)?;
        
        // 2. 计算运营费用
        let operator_amount = self.calculate_fee(net_profit, self.operator_fee_bps)?;
        
        // 3. 计算开发者费用
        let developer_amount = self.calculate_fee(net_profit, self.developer_fee_bps)?;
        
        // 4. 计算用户利润（剩余部分）
        let total_fees = protocol_amount + operator_amount + developer_amount;
        let user_amount = net_profit.saturating_sub(total_fees);
        
        // 5. 验证分配合理性
        self.validate_distribution_amounts(net_profit, &DistributionAmounts {
            user_amount,
            protocol_amount,
            operator_amount,
            developer_amount,
        })?;
        
        Ok(DistributionAmounts {
            user_amount,
            protocol_amount,
            operator_amount,
            developer_amount,
        })
    }
    
    /// 计算费用
    fn calculate_fee(&self, amount: u64, fee_bps: u16) -> Result<u64> {
        amount
            .checked_mul(fee_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero)
            .map_err(Into::into)
    }
    
    /// 执行转账操作
    fn execute_transfers<'info>(
        &self,
        source: &Account<'info, TokenAccount>,
        targets: &DistributionTargets<'info>,
        authority: &AccountInfo<'info>,
        authority_seeds: Option<&[&[&[u8]]]>,
        amounts: &DistributionAmounts,
    ) -> Result<()> {
        // 1. 转账给用户
        if amounts.user_amount > 0 {
            self.transfer_tokens(
                source,
                targets.user_account,
                authority,
                targets.token_program,
                amounts.user_amount,
                authority_seeds,
                "用户利润",
            )?;
        }
        
        // 2. 转账给协议
        if amounts.protocol_amount > 0 {
            self.transfer_tokens(
                source,
                targets.protocol_account,
                authority,
                targets.token_program,
                amounts.protocol_amount,
                authority_seeds,
                "协议费用",
            )?;
        }
        
        // 3. 转账给运营方（如果指定）
        if amounts.operator_amount > 0 {
            if let Some(operator_account) = targets.operator_account {
                self.transfer_tokens(
                    source,
                    operator_account,
                    authority,
                    targets.token_program,
                    amounts.operator_amount,
                    authority_seeds,
                    "运营费用",
                )?;
            }
        }
        
        // 4. 转账给开发者（如果指定）
        if amounts.developer_amount > 0 {
            if let Some(developer_account) = targets.developer_account {
                self.transfer_tokens(
                    source,
                    developer_account,
                    authority,
                    targets.token_program,
                    amounts.developer_amount,
                    authority_seeds,
                    "开发者费用",
                )?;
            }
        }
        
        Ok(())
    }
    
    /// 执行单次转账
    fn transfer_tokens<'info>(
        &self,
        from: &Account<'info, TokenAccount>,
        to: &Account<'info, TokenAccount>,
        authority: &AccountInfo<'info>,
        token_program: &Program<'info, Token>,
        amount: u64,
        authority_seeds: Option<&[&[&[u8]]]>,
        transfer_type: &str,
    ) -> Result<()> {
        msg!("执行{}转账: {} tokens", transfer_type, amount);
        
        let transfer_ctx = if let Some(seeds) = authority_seeds {
            CpiContext::new_with_signer(
                token_program.to_account_info(),
                Transfer {
                    from: from.to_account_info(),
                    to: to.to_account_info(),
                    authority: authority.clone(),
                },
                seeds,
            )
        } else {
            CpiContext::new(
                token_program.to_account_info(),
                Transfer {
                    from: from.to_account_info(),
                    to: to.to_account_info(),
                    authority: authority.clone(),
                },
            )
        };
        
        token::transfer(transfer_ctx, amount)
            .map_err(|e| {
                msg!("{}转账失败: {:?}", transfer_type, e);
                e
            })?;
        
        msg!("{}转账成功", transfer_type);
        Ok(())
    }
    
    /// 验证分配目标账户
    fn validate_distribution_targets(&self, targets: &DistributionTargets) -> Result<()> {
        // 验证所有账户使用相同的mint
        let user_mint = targets.user_account.mint;
        let protocol_mint = targets.protocol_account.mint;
        
        if user_mint != protocol_mint {
            return Err(RouteError::InvalidMintAccount.into());
        }
        
        // 验证可选账户的mint
        if let Some(operator_account) = targets.operator_account {
            if operator_account.mint != user_mint {
                return Err(RouteError::InvalidMintAccount.into());
            }
        }
        
        if let Some(developer_account) = targets.developer_account {
            if developer_account.mint != user_mint {
                return Err(RouteError::InvalidMintAccount.into());
            }
        }
        
        Ok(())
    }
    
    /// 验证分配金额的合理性
    fn validate_distribution_amounts(
        &self,
        total_profit: u64,
        amounts: &DistributionAmounts,
    ) -> Result<()> {
        let total_distributed = amounts.user_amount 
            + amounts.protocol_amount
            + amounts.operator_amount
            + amounts.developer_amount;
        
        // 检查总分配不超过总利润
        if total_distributed > total_profit {
            msg!("分配金额超出总利润 - 分配: {}, 总利润: {}", total_distributed, total_profit);
            return Err(RouteError::InvalidDistributionAmount.into());
        }
        
        // 检查用户至少获得50%的利润
        let min_user_share = total_profit / 2;
        if amounts.user_amount < min_user_share {
            msg!("用户分得利润过少 - 实际: {}, 最低: {}", amounts.user_amount, min_user_share);
            return Err(RouteError::InsufficientUserProfit.into());
        }
        
        // 检查各项费用不超过合理范围
        let max_protocol_fee = total_profit / 100; // 最大1%
        if amounts.protocol_amount > max_protocol_fee {
            return Err(RouteError::ExcessiveProtocolFee.into());
        }
        
        Ok(())
    }
    
    /// 计算利润分享比例
    pub fn calculate_profit_sharing_ratio(
        &self,
        user_contribution: u64,
        total_capital: u64,
        base_share_percentage: u8,
    ) -> Result<u8> {
        if total_capital == 0 {
            return Ok(base_share_percentage);
        }
        
        let contribution_ratio = (user_contribution as f64 / total_capital as f64) * 100.0;
        let bonus_ratio = (contribution_ratio * 0.2) as u8; // 20%的贡献奖励
        
        let final_share = (base_share_percentage + bonus_ratio).min(95); // 最高95%
        
        msg!("利润分享比例计算 - 基础: {}%, 贡献奖励: {}%, 最终: {}%", 
            base_share_percentage, bonus_ratio, final_share);
        
        Ok(final_share)
    }
    
    /// 设置费率
    pub fn set_fee_rates(&mut self, protocol_bps: u16, operator_bps: u16, developer_bps: u16) -> Result<()> {
        // 验证总费率不超过5%
        let total_fee_bps = protocol_bps + operator_bps + developer_bps;
        if total_fee_bps > 500 {
            return Err(RouteError::ExcessiveFeeRate.into());
        }
        
        self.protocol_fee_bps = protocol_bps;
        self.operator_fee_bps = operator_bps;
        self.developer_fee_bps = developer_bps;
        
        msg!("费率已更新 - 协议: {} bps, 运营: {} bps, 开发者: {} bps", 
            protocol_bps, operator_bps, developer_bps);
        
        Ok(())
    }
    
    /// 获取费率信息
    pub fn get_fee_rates(&self) -> FeeRates {
        FeeRates {
            protocol_fee_bps: self.protocol_fee_bps,
            operator_fee_bps: self.operator_fee_bps,
            developer_fee_bps: self.developer_fee_bps,
            total_fee_bps: self.protocol_fee_bps + self.operator_fee_bps + self.developer_fee_bps,
        }
    }
}

/// 分配金额（内部使用）
#[derive(Debug, Clone)]
struct DistributionAmounts {
    user_amount: u64,
    protocol_amount: u64,
    operator_amount: u64,
    developer_amount: u64,
}

/// 费率信息
#[derive(Debug, Clone)]
pub struct FeeRates {
    /// 协议费率
    pub protocol_fee_bps: u16,
    /// 运营费率
    pub operator_fee_bps: u16,
    /// 开发者费率
    pub developer_fee_bps: u16,
    /// 总费率
    pub total_fee_bps: u16,
}

/// 利润分配事件
#[event]
pub struct ProfitDistributedEvent {
    /// 用户
    pub user: Pubkey,
    /// 总利润
    pub total_profit: u64,
    /// 用户获得金额
    pub user_amount: u64,
    /// 协议费用
    pub protocol_amount: u64,
    /// 运营费用
    pub operator_amount: u64,
    /// 开发者费用
    pub developer_amount: u64,
    /// 代币mint
    pub mint: Pubkey,
    /// 时间戳
    pub timestamp: i64,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_profit_distributor_creation() {
        let config = crate::arbitrage::ArbitrageConfig::default();
        let distributor = ProfitDistributor::new(config);
        
        assert_eq!(distributor.protocol_fee_bps, 30);
        assert_eq!(distributor.operator_fee_bps, 20);
        assert_eq!(distributor.developer_fee_bps, 10);
    }

    #[test]
    fn test_fee_calculation() {
        let config = crate::arbitrage::ArbitrageConfig::default();
        let distributor = ProfitDistributor::new(config);
        
        let amount = 1_000_000; // 0.001 SOL
        let fee = distributor.calculate_fee(amount, 30); // 0.3%
        
        assert!(fee.is_ok());
        assert_eq!(fee.unwrap(), 300); // 0.0003 SOL
    }

    #[test]
    fn test_distribution_calculation() {
        let config = crate::arbitrage::ArbitrageConfig::default();
        let distributor = ProfitDistributor::new(config);
        
        let net_profit = 10_000_000; // 0.01 SOL
        let amounts = distributor.calculate_distribution_amounts(net_profit);
        
        assert!(amounts.is_ok());
        let dist = amounts.unwrap();
        
        // 验证总分配不超过总利润
        let total = dist.user_amount + dist.protocol_amount + dist.operator_amount + dist.developer_amount;
        assert!(total <= net_profit);
        
        // 验证用户获得大部分利润
        assert!(dist.user_amount > net_profit / 2);
    }

    #[test]
    fn test_fee_rate_setting() {
        let config = crate::arbitrage::ArbitrageConfig::default();
        let mut distributor = ProfitDistributor::new(config);
        
        // 正常费率应该成功
        assert!(distributor.set_fee_rates(50, 30, 20).is_ok());
        
        // 过高费率应该失败
        assert!(distributor.set_fee_rates(300, 300, 300).is_err());
    }
}
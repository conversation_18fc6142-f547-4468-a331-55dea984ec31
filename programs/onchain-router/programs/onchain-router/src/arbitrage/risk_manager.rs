//! 套利风险管理器
//!
//! 负责动态风险评估、限制管理和风险控制

use std::collections::BTreeMap;
use anchor_lang::prelude::*;
use crate::routing::types::{Route, Dex};
use crate::error::RouteError;
use super::{ArbitragePath, ArbitrageConfig, MarketConditions};

/// 风险评估结果
#[derive(AnchorSerialize, AnchorDeserialize, Debug, Clone, Default)]
pub struct RiskAssessment {
    /// 总体风险评分（0-100）
    pub overall_risk_score: u8,
    /// 流动性风险
    pub liquidity_risk: RiskLevel,
    /// 价格风险
    pub price_risk: RiskLevel,
    /// 技术风险
    pub technical_risk: RiskLevel,
    /// 时间风险
    pub timing_risk: RiskLevel,
    /// 是否建议执行
    pub recommended: bool,
    /// 风险警告
    pub warnings: Vec<String>,
    /// 建议的最大交易金额
    pub max_recommended_amount: u64,
}

/// 风险等级
#[derive(AnchorSerialize, AnchorDeserialize, Debug, Clone, Copy, PartialEq)]
pub enum RiskLevel {
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3,
}

impl Default for RiskLevel {
    fn default() -> Self {
        RiskLevel::Low
    }
}

/// 风险限制配置
#[derive(Debug, Clone)]
pub struct RiskLimits {
    /// 最大总风险评分
    pub max_risk_score: u8,
    /// 最大单笔交易金额
    pub max_single_trade_amount: u64,
    /// 最大每日交易金额
    pub max_daily_volume: u64,
    /// 最大滑点容忍度（基点）
    pub max_slippage_bps: u16,
    /// 最大路径长度
    pub max_path_length: usize,
    /// 最小流动性要求
    pub min_liquidity_threshold: u64,
}

impl Default for RiskLimits {
    fn default() -> Self {
        Self {
            max_risk_score: 70,
            max_single_trade_amount: 10_000_000_000, // 10 SOL
            max_daily_volume: 100_000_000_000, // 100 SOL
            max_slippage_bps: 500, // 5%
            max_path_length: 4,
            min_liquidity_threshold: 1_000_000_000, // 1 SOL
        }
    }
}

/// 市场风险指标
#[derive(Debug, Clone, Copy)]
pub struct MarketRiskMetrics {
    /// 价格波动率（0-100）
    pub volatility: u8,
    /// 流动性水平（0-100）
    pub liquidity_level: u8,
    /// 网络拥堵程度（0-100）
    pub network_congestion: u8,
    /// MEV竞争强度（0-100）
    pub mev_competition: u8,
    /// 最近失败率（0-100）
    pub recent_failure_rate: u8,
}

/// 风险管理器
pub struct RiskManager {
    /// 配置
    #[allow(dead_code)]
    config: ArbitrageConfig,
    /// 风险限制
    limits: RiskLimits,
    /// 每日交易统计
    daily_stats: BTreeMap<String, DailyStats>,
    /// DEX风险评级
    dex_risk_ratings: BTreeMap<Dex, u8>,
    /// 代币风险评级
    token_risk_ratings: BTreeMap<Pubkey, u8>,
    /// 市场风险指标
    market_metrics: MarketRiskMetrics,
    /// 历史执行统计
    execution_history: ExecutionHistory,
}

/// 每日统计
#[derive(Debug, Clone, Default)]
pub struct DailyStats {
    /// 交易总量
    pub total_volume: u64,
    /// 交易次数
    pub trade_count: u32,
    /// 成功次数
    pub success_count: u32,
    /// 总利润
    pub total_profit: i64,
}

/// 执行历史统计
#[derive(Debug, Clone, Default)]
pub struct ExecutionHistory {
    /// 总执行次数
    pub total_executions: u64,
    /// 成功次数
    pub successful_executions: u64,
    /// 平均执行时间（毫秒）
    pub avg_execution_time_ms: u64,
    /// 平均利润
    pub avg_profit: i64,
    /// 最大损失
    pub max_loss: u64,
}

impl RiskManager {
    /// 创建新的风险管理器
    pub fn new(config: ArbitrageConfig) -> Self {
        let mut dex_risk_ratings = BTreeMap::new();
        dex_risk_ratings.insert(Dex::RaydiumClmm, 20);
        dex_risk_ratings.insert(Dex::RaydiumCpmm, 22);
        dex_risk_ratings.insert(Dex::Orca, 25);
        dex_risk_ratings.insert(Dex::MeteoraLb, 28);
        dex_risk_ratings.insert(Dex::MeteoraAmm, 30);
        dex_risk_ratings.insert(Dex::PumpSwap, 60);

        Self {
            config,
            limits: RiskLimits::default(),
            daily_stats: BTreeMap::new(),
            dex_risk_ratings,
            token_risk_ratings: BTreeMap::new(),
            market_metrics: MarketRiskMetrics {
                volatility: 30,
                liquidity_level: 70,
                network_congestion: 20,
                mev_competition: 40,
                recent_failure_rate: 10,
            },
            execution_history: ExecutionHistory::default(),
        }
    }

    /// 评估套利路径风险
    pub fn assess_arbitrage_risk(
        &self,
        path: &ArbitragePath,
        trade_amount: u64,
        market_conditions: &MarketConditions,
    ) -> Result<RiskAssessment> {
        msg!("开始风险评估 - 交易金额: {}, 路径步骤: {}", trade_amount, path.routes.len());

        // 1. 基础风险检查
        self.check_basic_limits(trade_amount, &path.routes)?;

        // 2. 流动性风险评估
        let liquidity_risk = self.assess_liquidity_risk(&path.routes, trade_amount)?;

        // 3. 价格风险评估
        let price_risk = self.assess_price_risk(&path.routes, market_conditions)?;

        // 4. 技术风险评估
        let technical_risk = self.assess_technical_risk(&path.routes)?;

        // 5. 时间风险评估
        let timing_risk = self.assess_timing_risk(&path.routes, market_conditions)?;

        // 6. 计算总体风险评分
        let overall_risk_score = self.calculate_overall_risk_score(
            liquidity_risk,
            price_risk,
            technical_risk,
            timing_risk,
        );

        // 7. 生成风险警告
        let warnings = self.generate_risk_warnings(
            &path.routes,
            trade_amount,
            overall_risk_score,
        );

        // 8. 计算建议的最大交易金额
        let max_recommended_amount = self.calculate_max_recommended_amount(
            &path.routes,
            overall_risk_score,
        );

        // 9. 判断是否建议执行
        let recommended = overall_risk_score <= self.limits.max_risk_score
            && warnings.is_empty()
            && trade_amount <= max_recommended_amount;

        let assessment = RiskAssessment {
            overall_risk_score,
            liquidity_risk,
            price_risk,
            technical_risk,
            timing_risk,
            recommended,
            warnings,
            max_recommended_amount,
        };

        msg!("风险评估完成 - 总评分: {}, 建议执行: {}", overall_risk_score, recommended);

        Ok(assessment)
    }

    /// 检查基础限制
    fn check_basic_limits(&self, trade_amount: u64, routes: &[Route]) -> Result<()> {
        // 检查交易金额限制
        if trade_amount > self.limits.max_single_trade_amount {
            return Err(RouteError::TradingAmountExceeded.into());
        }

        // 检查路径长度限制
        if routes.len() > self.limits.max_path_length {
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 检查每日交易量限制
        let today = self.get_today_key();
        if let Some(stats) = self.daily_stats.get(&today) {
            let projected_volume = stats.total_volume + trade_amount;
            if projected_volume > self.limits.max_daily_volume {
                return Err(RouteError::DailyVolumeLimitExceeded.into());
            }
        }

        Ok(())
    }

    /// 评估流动性风险
    fn assess_liquidity_risk(&self, routes: &[Route], trade_amount: u64) -> Result<RiskLevel> {
        let mut total_risk_score = 0u32;

        for route in routes {
            // 获取交易对的流动性数据（简化实现）
            let estimated_liquidity = self.estimate_pair_liquidity(&route.input_mint, &route.output_mint);

            // 计算交易金额占流动性的比例
            let impact_ratio = if estimated_liquidity > 0 {
                (trade_amount as f64 / estimated_liquidity as f64) * 100.0
            } else {
                100.0
            };

            let risk_score = match impact_ratio as u32 {
                0..=5 => 10,      // 低影响
                6..=15 => 25,     // 中等影响
                16..=30 => 50,    // 高影响
                _ => 80,          // 极高影响
            };

            total_risk_score += risk_score;
        }

        let avg_risk_score = total_risk_score / routes.len() as u32;

        Ok(match avg_risk_score {
            0..=25 => RiskLevel::Low,
            26..=50 => RiskLevel::Medium,
            51..=75 => RiskLevel::High,
            _ => RiskLevel::Critical,
        })
    }

    /// 评估价格风险
    fn assess_price_risk(&self, routes: &[Route], market_conditions: &MarketConditions) -> Result<RiskLevel> {
        let mut risk_factors = 0u32;

        // 1. 市场波动性风险
        risk_factors += match market_conditions.volatility {
            0..=30 => 10,
            31..=60 => 25,
            61..=80 => 50,
            _ => 80,
        };

        // 2. 路径复杂度风险
        risk_factors += match routes.len() {
            2 => 5,
            3 => 15,
            4 => 30,
            _ => 50,
        };

        // 3. DEX多样性风险
        let mut dexes = [false; 6]; // 支持6种DEX类型
        for route in routes {
            let dex_index = match route.dex {
                Dex::RaydiumClmm => 0,
                Dex::RaydiumCpmm => 1,
                Dex::MeteoraLb => 2,
                Dex::MeteoraAmm => 3,
                Dex::Orca => 4,
                Dex::PumpSwap => 5,
            };
            dexes[dex_index] = true;
        }
        let unique_dexes = dexes.iter().filter(|&&x| x).count();

        risk_factors += match unique_dexes {
            1 => 20,    // 单一DEX风险
            2 => 10,
            3 => 5,
            _ => 0,
        };

        Ok(match risk_factors {
            0..=30 => RiskLevel::Low,
            31..=60 => RiskLevel::Medium,
            61..=90 => RiskLevel::High,
            _ => RiskLevel::Critical,
        })
    }

    /// 评估技术风险
    fn assess_technical_risk(&self, routes: &[Route]) -> Result<RiskLevel> {
        let mut risk_score = 0u32;

        for route in routes {
            // DEX风险评级
            let dex_risk = self.dex_risk_ratings.get(&route.dex).unwrap_or(&50);
            risk_score += *dex_risk as u32;

            // 代币风险评级
            let input_token_risk = self.token_risk_ratings.get(&route.input_mint).unwrap_or(&20);
            let output_token_risk = self.token_risk_ratings.get(&route.output_mint).unwrap_or(&20);
            risk_score += (*input_token_risk + *output_token_risk) as u32;
        }

        let avg_risk_score = risk_score / (routes.len() as u32 * 3); // 3个风险因子

        Ok(match avg_risk_score {
            0..=20 => RiskLevel::Low,
            21..=40 => RiskLevel::Medium,
            41..=60 => RiskLevel::High,
            _ => RiskLevel::Critical,
        })
    }

    /// 评估时间风险
    fn assess_timing_risk(&self, routes: &[Route], market_conditions: &MarketConditions) -> Result<RiskLevel> {
        let mut risk_factors = 0u32;

        // 1. 网络拥堵风险
        risk_factors += match market_conditions.network_congestion {
            0..=30 => 5,
            31..=60 => 20,
            61..=80 => 40,
            _ => 70,
        };

        // 2. 执行复杂度风险
        let execution_complexity = routes.len() * 10; // 每步增加10分
        risk_factors += execution_complexity as u32;

        // 3. MEV竞争风险
        risk_factors += self.market_metrics.mev_competition as u32;

        // 4. 历史失败率风险
        risk_factors += self.market_metrics.recent_failure_rate as u32;

        Ok(match risk_factors {
            0..=40 => RiskLevel::Low,
            41..=80 => RiskLevel::Medium,
            81..=120 => RiskLevel::High,
            _ => RiskLevel::Critical,
        })
    }

    /// 计算总体风险评分
    fn calculate_overall_risk_score(
        &self,
        liquidity_risk: RiskLevel,
        price_risk: RiskLevel,
        technical_risk: RiskLevel,
        timing_risk: RiskLevel,
    ) -> u8 {
        let liquidity_weight = 0.3;
        let price_weight = 0.3;
        let technical_weight = 0.2;
        let timing_weight = 0.2;

        let liquidity_score = (liquidity_risk as u8) * 25;
        let price_score = (price_risk as u8) * 25;
        let technical_score = (technical_risk as u8) * 25;
        let timing_score = (timing_risk as u8) * 25;

        let weighted_score = (liquidity_score as f64 * liquidity_weight)
            + (price_score as f64 * price_weight)
            + (technical_score as f64 * technical_weight)
            + (timing_score as f64 * timing_weight);

        weighted_score.min(100.0) as u8
    }

    /// 生成风险警告
    fn generate_risk_warnings(
        &self,
        routes: &[Route],
        trade_amount: u64,
        risk_score: u8,
    ) -> Vec<String> {
        let mut warnings = Vec::new();

        // 高风险评分警告
        if risk_score > 80 {
            warnings.push("极高风险：建议暂停交易".to_string());
        } else if risk_score > 60 {
            warnings.push("高风险：建议降低交易金额".to_string());
        }

        // 大额交易警告
        if trade_amount > self.limits.max_single_trade_amount / 2 {
            warnings.push("大额交易：注意流动性影响".to_string());
        }

        // 复杂路径警告
        if routes.len() > 3 {
            warnings.push("复杂路径：执行失败风险较高".to_string());
        }

        // 高波动性警告
        if self.market_metrics.volatility > 70 {
            warnings.push("市场高波动：价格风险增加".to_string());
        }

        // 网络拥堵警告
        if self.market_metrics.network_congestion > 70 {
            warnings.push("网络拥堵：执行可能延迟".to_string());
        }

        warnings
    }

    /// 计算建议的最大交易金额
    fn calculate_max_recommended_amount(&self, routes: &[Route], risk_score: u8) -> u64 {
        let base_amount = self.limits.max_single_trade_amount;

        // 根据风险评分调整
        let risk_factor = match risk_score {
            0..=30 => 1.0,
            31..=50 => 0.7,
            51..=70 => 0.4,
            71..=85 => 0.2,
            _ => 0.1,
        };

        // 根据路径复杂度调整
        let complexity_factor = match routes.len() {
            2 => 1.0,
            3 => 0.8,
            4 => 0.6,
            _ => 0.4,
        };

        // 根据市场条件调整
        let market_factor = if self.market_metrics.volatility > 60 { 0.7 } else { 1.0 };

        let adjusted_amount = (base_amount as f64 * risk_factor * complexity_factor * market_factor) as u64;
        adjusted_amount.max(1_000_000) // 最低0.001 SOL
    }

    /// 估算交易对流动性
    fn estimate_pair_liquidity(&self, input_mint: &Pubkey, output_mint: &Pubkey) -> u64 {
        // 简化的流动性估算（实际应查询链上数据）
        if input_mint == output_mint {
            return u64::MAX;
        }

        // 基于代币地址生成模拟流动性数据
        let hash = input_mint.to_bytes().iter()
            .zip(output_mint.to_bytes().iter())
            .map(|(a, b)| a.wrapping_add(*b))
            .sum::<u8>();

        match hash % 4 {
            0 => 100_000_000_000, // 100 SOL 高流动性
            1 => 10_000_000_000,  // 10 SOL 中等流动性
            2 => 1_000_000_000,   // 1 SOL 低流动性
            _ => 100_000_000,     // 0.1 SOL 极低流动性
        }
    }

    /// 获取今日键值
    fn get_today_key(&self) -> String {
        // 简化实现，实际应该使用真实日期
        "2024-01-01".to_string()
    }

    /// 更新执行统计
    pub fn update_execution_stats(
        &mut self,
        success: bool,
        profit: i64,
        execution_time_ms: u64,
        trade_amount: u64,
    ) {
        // 更新历史统计
        self.execution_history.total_executions += 1;
        if success {
            self.execution_history.successful_executions += 1;
        }

        // 更新平均执行时间
        let total_time = self.execution_history.avg_execution_time_ms
            * (self.execution_history.total_executions - 1) + execution_time_ms;
        self.execution_history.avg_execution_time_ms = total_time / self.execution_history.total_executions;

        // 更新平均利润
        let total_profit = self.execution_history.avg_profit
            * (self.execution_history.total_executions as i64 - 1) + profit;
        self.execution_history.avg_profit = total_profit / self.execution_history.total_executions as i64;

        // 更新最大损失
        if profit < 0 && (-profit) as u64 > self.execution_history.max_loss {
            self.execution_history.max_loss = (-profit) as u64;
        }

        // 更新每日统计
        let today = self.get_today_key();
        let stats = self.daily_stats.entry(today).or_default();
        stats.total_volume += trade_amount;
        stats.trade_count += 1;
        if success {
            stats.success_count += 1;
        }
        stats.total_profit += profit;
    }

    /// 更新市场风险指标
    pub fn update_market_metrics(&mut self, metrics: MarketRiskMetrics) {
        let volatility = metrics.volatility;
        let liquidity_level = metrics.liquidity_level;
        let network_congestion = metrics.network_congestion;

        self.market_metrics = metrics;
        msg!("市场风险指标已更新 - 波动性: {}, 流动性: {}, 拥堵: {}",
            volatility, liquidity_level, network_congestion);
    }

    /// 设置DEX风险评级
    pub fn set_dex_risk_rating(&mut self, dex: Dex, rating: u8) {
        self.dex_risk_ratings.insert(dex, rating.min(100));
        msg!("DEX风险评级已更新: {:?} = {}", dex, rating);
    }

    /// 设置代币风险评级
    pub fn set_token_risk_rating(&mut self, token: Pubkey, rating: u8) {
        self.token_risk_ratings.insert(token, rating.min(100));
        msg!("代币风险评级已更新: {} = {}", token, rating);
    }

    /// 获取风险管理统计
    pub fn get_risk_stats(&self) -> RiskStats {
        let success_rate = if self.execution_history.total_executions > 0 {
            (self.execution_history.successful_executions as f64 / self.execution_history.total_executions as f64) * 100.0
        } else {
            0.0
        };

        RiskStats {
            total_executions: self.execution_history.total_executions,
            success_rate,
            avg_profit: self.execution_history.avg_profit,
            max_loss: self.execution_history.max_loss,
            avg_execution_time_ms: self.execution_history.avg_execution_time_ms,
            current_market_volatility: self.market_metrics.volatility,
        }
    }
}

/// 风险管理统计
#[derive(Debug, Clone)]
pub struct RiskStats {
    /// 总执行次数
    pub total_executions: u64,
    /// 成功率（百分比）
    pub success_rate: f64,
    /// 平均利润
    pub avg_profit: i64,
    /// 最大损失
    pub max_loss: u64,
    /// 平均执行时间
    pub avg_execution_time_ms: u64,
    /// 当前市场波动性
    pub current_market_volatility: u8,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_risk_manager_creation() {
        let config = ArbitrageConfig::default();
        let risk_manager = RiskManager::new(config);

        assert!(risk_manager.dex_risk_ratings.contains_key(&Dex::RaydiumClmm));
        assert_eq!(risk_manager.limits.max_risk_score, 70);
    }

    #[test]
    fn test_basic_limits_check() {
        let config = ArbitrageConfig::default();
        let risk_manager = RiskManager::new(config);

        let routes = vec![
            Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![],
                min_amount_out: 900,
            },
        ];

        // 正常交易金额应该通过
        assert!(risk_manager.check_basic_limits(1_000_000_000, &routes).is_ok());

        // 过大的交易金额应该失败
        assert!(risk_manager.check_basic_limits(100_000_000_000, &routes).is_err());
    }

    #[test]
    fn test_overall_risk_score_calculation() {
        let config = ArbitrageConfig::default();
        let risk_manager = RiskManager::new(config);

        let score = risk_manager.calculate_overall_risk_score(
            RiskLevel::Low,
            RiskLevel::Medium,
            RiskLevel::Low,
            RiskLevel::Low,
        );

        assert!(score <= 100);
        assert!(score > 0);
    }
}

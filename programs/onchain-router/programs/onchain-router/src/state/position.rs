//! 用户位置状态
//! 
//! 存储用户的交易历史和风险评级

use anchor_lang::prelude::*;

/// 用户位置和统计信息
#[account]
pub struct UserPosition {
    /// 用户公钥
    pub user: Pubkey,
    
    /// 是否被暂停
    pub is_suspended: bool,
    
    /// 风险等级 (1-5, 1为最低风险)
    pub risk_level: u8,
    
    /// 总交易量
    pub total_volume: u64,
    
    /// 总利润
    pub total_profit: u64,
    
    /// 总亏损
    pub total_loss: u64,
    
    /// 成功路由次数
    pub successful_routes: u32,
    
    /// 失败路由次数
    pub failed_routes: u32,
    
    /// 成功交易次数
    pub total_successful_trades: u64,
    
    /// 失败交易次数
    pub total_failed_trades: u64,
    
    /// 日交易量使用量
    pub daily_volume_used: u64,
    
    /// 上次活动时间
    pub last_activity_time: i64,
    
    /// 活动截止时间（可选）
    pub last_activity_deadline: Option<i64>,
    
    /// 创建时间
    pub created_at: i64,
    
    /// 最后活动时间
    pub last_activity: i64,
    
    /// 保留字段用于未来扩展
    pub reserved: [u64; 8],
}

impl UserPosition {
    /// 计算账户所需空间
    pub const LEN: usize = 8 + // discriminator
        32 + // user
        1 + // is_suspended
        1 + // risk_level
        8 + // total_volume
        8 + // total_profit
        8 + // total_loss
        4 + // successful_routes
        4 + // failed_routes
        8 + // total_successful_trades
        8 + // total_failed_trades
        8 + // daily_volume_used
        8 + // last_activity_time
        1 + 8 + // last_activity_deadline (Option<i64>)
        8 + // created_at
        8 + // last_activity
        8 * 8; // reserved
    
    /// 计算成功率（百分比）
    pub fn success_rate(&self) -> u8 {
        let total_routes = self.successful_routes + self.failed_routes;
        if total_routes == 0 {
            return 100; // 新用户默认100%
        }
        
        ((self.successful_routes as u64 * 100) / total_routes as u64) as u8
    }
    
    /// 计算净利润
    pub fn net_profit(&self) -> i64 {
        (self.total_profit as i64) - (self.total_loss as i64)
    }
    
    /// 更新活动时间
    pub fn touch(&mut self) -> Result<()> {
        self.last_activity = Clock::get()?.unix_timestamp;
        Ok(())
    }
    
    /// 记录成功的路由
    pub fn record_successful_route(&mut self, volume: u64, profit: u64) -> Result<()> {
        self.total_volume = self.total_volume.checked_add(volume)
            .ok_or(crate::error::RouteError::MathOverflow)?;
        self.total_profit = self.total_profit.checked_add(profit)
            .ok_or(crate::error::RouteError::MathOverflow)?;
        self.successful_routes = self.successful_routes.checked_add(1)
            .ok_or(crate::error::RouteError::MathOverflow)?;
        self.touch()?;
        Ok(())
    }
    
    /// 记录失败的路由
    pub fn record_failed_route(&mut self, volume: u64, loss: u64) -> Result<()> {
        self.total_volume = self.total_volume.checked_add(volume)
            .ok_or(crate::error::RouteError::MathOverflow)?;
        self.total_loss = self.total_loss.checked_add(loss)
            .ok_or(crate::error::RouteError::MathOverflow)?;
        self.failed_routes = self.failed_routes.checked_add(1)
            .ok_or(crate::error::RouteError::MathOverflow)?;
        self.touch()?;
        Ok(())
    }
    
    /// 评估用户风险等级
    pub fn assess_risk_level(&mut self) -> Result<()> {
        let success_rate = self.success_rate();
        let total_routes = self.successful_routes + self.failed_routes;
        
        // 基于成功率和交易次数评估风险等级
        self.risk_level = if total_routes < 10 {
            3 // 新用户中等风险
        } else if success_rate >= 90 {
            1 // 低风险
        } else if success_rate >= 70 {
            2 // 中低风险
        } else if success_rate >= 50 {
            3 // 中等风险
        } else if success_rate >= 30 {
            4 // 中高风险
        } else {
            5 // 高风险
        };
        
        Ok(())
    }
    
    /// 检查是否可以执行指定金额的交易
    pub fn can_execute_amount(&self, amount: u64, max_amount_per_risk: &[u64; 5]) -> bool {
        if self.is_suspended {
            return false;
        }
        
        let risk_index = (self.risk_level as usize).saturating_sub(1).min(4);
        amount <= max_amount_per_risk[risk_index]
    }
    
    /// 获取用户等级描述
    pub fn risk_level_description(&self) -> &'static str {
        match self.risk_level {
            1 => "低风险",
            2 => "中低风险", 
            3 => "中等风险",
            4 => "中高风险",
            5 => "高风险",
            _ => "未知",
        }
    }
}
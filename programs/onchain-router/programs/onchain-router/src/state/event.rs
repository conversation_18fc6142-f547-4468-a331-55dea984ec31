//! 事件定义
//! 
//! 定义智能合约发出的所有事件结构

use anchor_lang::prelude::*;
use crate::routing::types::{RoutingM<PERSON>, Dex};

/// 路由执行成功事件
#[event]
pub struct RouteExecuted {
    pub user: Pubkey,
    pub order_id: u64,
    pub route_mode: RoutingMode,
    pub amount_in: u64,
    pub amount_out: u64,
    pub steps: u8,
    pub dex_path: Vec<Dex>,
    pub execution_time_ms: u64,
    pub gas_used: u64,
    pub actual_slippage_bps: u16,
    pub timestamp: i64,
}

/// 路由执行失败事件
#[event]
pub struct RouteFailed {
    pub user: Pubkey,
    pub order_id: u64,
    pub error_code: u32,
    pub failed_step: u8,
    pub failed_dex: Dex,
    pub amount_at_failure: u64,
    pub timestamp: i64,
}

/// 闪电贷执行事件
#[event]
pub struct FlashLoanExecuted {
    pub user: Pubkey,
    pub provider: Pubkey,
    pub amount: u64,
    pub fee: u64,
    pub success: bool,
    pub order_id: u64,
    pub timestamp: i64,
}

/// 配置更新事件
#[event]
pub struct ConfigUpdated {
    pub admin: Pubkey,
    pub field: String,
    pub old_value: String,
    pub new_value: String,
    pub timestamp: i64,
}

/// 紧急停止事件
#[event]
pub struct EmergencyStop {
    pub admin: Pubkey,
    pub stop_type: EmergencyStopType,
    pub target: Option<Dex>,
    pub reason: String,
    pub timestamp: i64,
}

/// 用户暂停事件
#[event]
pub struct UserSuspended {
    pub admin: Pubkey,
    pub user: Pubkey,
    pub reason: String,
    pub timestamp: i64,
}

/// 紧急停止类型
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum EmergencyStopType {
    Global,
    DexSpecific,
}

/// 套利成功事件（特殊的路由事件）
#[event]
pub struct ArbitrageSuccess {
    pub user: Pubkey,
    pub order_id: u64,
    pub initial_amount: u64,
    pub final_amount: u64,
    pub net_profit: u64,
    pub flash_loan_used: bool,
    pub flash_loan_amount: u64,
    pub flash_loan_fee: u64,
    pub dex_path: Vec<Dex>,
    pub execution_time_ms: u64,
    pub timestamp: i64,
}

/// 风险评级更新事件
#[event]
pub struct RiskLevelUpdated {
    pub user: Pubkey,
    pub old_level: u8,
    pub new_level: u8,
    pub success_rate: u8,
    pub total_volume: u64,
    pub timestamp: i64,
}

/// DEX操作事件
#[event]
pub struct DexOperationEvent {
    pub user: Pubkey,
    pub order_id: u64,
    pub dex: Dex,
    pub operation_type: String,
    pub token_in: Pubkey,
    pub token_out: Pubkey,
    pub amount_in: u64,
    pub amount_out: u64,
    pub price_impact_bps: u16,
    pub success: bool,
    pub error_code: Option<u32>,
    pub execution_time_ms: u64,
    pub timestamp: i64,
}

/// 流动性监控事件
#[event]
pub struct LiquidityEvent {
    pub dex: Dex,
    pub pool_address: Pubkey,
    pub token_a: Pubkey,
    pub token_b: Pubkey,
    pub liquidity_before: u64,
    pub liquidity_after: u64,
    pub change_percentage: i16, // 可以为负数
    pub impact_severity: LiquidityImpactSeverity,
    pub timestamp: i64,
}

/// 价格影响事件
#[event]
pub struct PriceImpactEvent {
    pub user: Pubkey,
    pub order_id: u64,
    pub dex: Dex,
    pub pool_address: Pubkey,
    pub amount_in: u64,
    pub expected_out: u64,
    pub actual_out: u64,
    pub price_impact_bps: u16,
    pub slippage_bps: u16,
    pub severity: PriceImpactSeverity,
    pub timestamp: i64,
}

/// 套利机会检测事件
#[event]
pub struct ArbitrageOpportunityDetected {
    pub token_pair: [Pubkey; 2],
    pub dex_path: Vec<Dex>,
    pub pool_addresses: Vec<Pubkey>,
    pub expected_profit: u64,
    pub required_amount: u64,
    pub profit_margin_bps: u16,
    pub confidence_score: u8,
    pub discovery_method: String,
    pub valid_until: i64,
    pub timestamp: i64,
}

/// 错误恢复事件
#[event]
pub struct ErrorRecoveryEvent {
    pub user: Pubkey,
    pub order_id: u64,
    pub original_error_code: u32,
    pub recovery_action: String,
    pub attempt_number: u8,
    pub success: bool,
    pub execution_time_ms: u64,
    pub additional_context: String,
    pub timestamp: i64,
}

/// 系统性能事件
#[event]
pub struct PerformanceMetrics {
    pub operation_type: String,
    pub user_count: u32,
    pub total_volume: u64,
    pub average_execution_time_ms: u64,
    pub success_rate_bps: u16,
    pub error_rate_bps: u16,
    pub gas_efficiency_score: u8,
    pub period_start: i64,
    pub period_end: i64,
    pub timestamp: i64,
}

/// 安全警报事件
#[event]
pub struct SecurityAlert {
    pub alert_type: SecurityAlertType,
    pub severity: SecuritySeverity,
    pub user: Option<Pubkey>,
    pub suspected_attack: String,
    pub evidence: String,
    pub automatic_action_taken: bool,
    pub requires_manual_review: bool,
    pub timestamp: i64,
}

/// 资金流动事件
#[event]
pub struct FundsFlowEvent {
    pub user: Pubkey,
    pub order_id: u64,
    pub flow_type: FundsFlowType,
    pub token: Pubkey,
    pub amount: u64,
    pub source_account: Pubkey,
    pub destination_account: Pubkey,
    pub transaction_signature: String,
    pub timestamp: i64,
}

/// 批量路由执行事件
#[event]
pub struct BatchRouteExecuted {
    pub user: Pubkey,
    pub batch_id: u64,
    pub total_routes: u8,
    pub successful_routes: u8,
    pub failed_routes: u8,
    pub total_profit: i64, // 可以为负数表示亏损
    pub total_gas_used: u64,
    pub batch_execution_time_ms: u64,
    pub average_route_time_ms: u64,
    pub timestamp: i64,
}

/// 市场状态变化事件
#[event]
pub struct MarketStateChanged {
    pub dex: Dex,
    pub pool_address: Pubkey,
    pub token_pair: [Pubkey; 2],
    pub old_price: u64,
    pub new_price: u64,
    pub price_change_bps: i16,
    pub volume_24h: u64,
    pub volatility_score: u8,
    pub market_trend: MarketTrend,
    pub timestamp: i64,
}

/// 流动性影响严重程度
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum LiquidityImpactSeverity {
    Low,      // <5% 影响
    Medium,   // 5-15% 影响
    High,     // 15-30% 影响
    Critical, // >30% 影响
}

/// 价格影响严重程度
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum PriceImpactSeverity {
    Minimal,   // <1% 影响
    Low,       // 1-3% 影响
    Medium,    // 3-10% 影响
    High,      // 10-20% 影响
    Extreme,   // >20% 影响
}

/// 安全警报类型
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum SecurityAlertType {
    SuspiciousPattern,
    UnusualVolume,
    RapidRepeatedTransactions,
    LargeAmountTransfer,
    CrossDexArbitrage,
    PotentialFlashLoanAttack,
    ReentrancyAttempt,
    UnauthorizedAccess,
}

/// 安全严重程度
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum SecuritySeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 资金流动类型
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum FundsFlowType {
    Deposit,
    Withdrawal,
    SwapIn,
    SwapOut,
    FlashLoanBorrow,
    FlashLoanRepay,
    FeesCollected,
    ProfitWithdrawal,
}

/// 市场趋势
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum MarketTrend {
    Bullish,
    Bearish,
    Sideways,
    Volatile,
}

/// 事件发射辅助函数
impl RouteExecuted {
    pub fn emit_success(
        user: Pubkey,
        order_id: u64,
        route_mode: RoutingMode,
        amount_in: u64,
        amount_out: u64,
        steps: u8,
        dex_path: Vec<Dex>,
        execution_time_ms: u64,
        gas_used: u64,
        actual_slippage_bps: u16,
    ) {
        emit!(RouteExecuted {
            user,
            order_id,
            route_mode,
            amount_in,
            amount_out,
            steps,
            dex_path,
            execution_time_ms,
            gas_used,
            actual_slippage_bps,
            timestamp: Clock::get().map(|c| c.unix_timestamp).unwrap_or(0),
        });
    }
}

impl RouteFailed {
    pub fn emit_failure(
        user: Pubkey,
        order_id: u64,
        error_code: u32,
        failed_step: u8,
        failed_dex: Dex,
        amount_at_failure: u64,
    ) {
        emit!(RouteFailed {
            user,
            order_id,
            error_code,
            failed_step,
            failed_dex,
            amount_at_failure,
            timestamp: Clock::get().map(|c| c.unix_timestamp).unwrap_or(0),
        });
    }
}

impl SecurityAlert {
    pub fn emit_alert(
        alert_type: SecurityAlertType,
        severity: SecuritySeverity,
        user: Option<Pubkey>,
        suspected_attack: String,
        evidence: String,
    ) {
        let requires_manual_review = matches!(severity, SecuritySeverity::High | SecuritySeverity::Critical);
        emit!(SecurityAlert {
            alert_type,
            severity,
            user,
            suspected_attack,
            evidence,
            automatic_action_taken: false,
            requires_manual_review,
            timestamp: Clock::get().map(|c| c.unix_timestamp).unwrap_or(0),
        });
    }
}
//! 路由器配置状态
//!
//! 存储全局配置参数和控制状态

use anchor_lang::prelude::*;
use crate::routing::types::Dex;

/// 路由器全局配置
#[account]
pub struct RouterConfig {
    /// 管理员公钥
    pub admin: Pubkey,

    /// 支持的DEX列表
    pub supported_dexes: Vec<Dex>,

    /// 最大单次路由金额
    pub max_route_amount: u64,

    /// 最大闪电贷金额
    pub max_flash_loan_amount: u64,

    /// 最大允许滑点（基点，如300表示3%）
    pub max_slippage_bps: u16,

    /// 最小利润阈值
    pub min_profit_threshold: u64,

    /// 最大Gas费用
    pub max_gas_fee: u64,

    /// 协议费率（基点）
    pub protocol_fee_bps: u16,

    /// 最小路由金额
    pub min_route_amount: u64,

    /// 最大路由步骤数
    pub max_route_steps: u8,

    /// 最大复杂度评分
    pub max_complexity_score: u8,

    /// 最大风险评分
    pub max_risk_score: u8,

    /// 用户最大日交易量
    pub max_daily_volume_per_user: u64,

    /// 全局紧急停止开关
    pub emergency_stop: bool,

    /// 单个DEX紧急停止列表
    pub dex_emergency_stops: Vec<Dex>,

    /// 创建时间
    pub created_at: i64,

    /// 最后更新时间
    pub updated_at: i64,

    /// 保留字段用于未来扩展
    pub reserved: [u64; 8],
}

impl RouterConfig {
    /// 计算账户所需空间
    pub const LEN: usize = 8 + // discriminator
        32 + // admin
        4 + 6 * 1 + // supported_dexes (Vec<Dex>)
        8 + // max_route_amount
        8 + // max_flash_loan_amount
        2 + // max_slippage_bps
        8 + // min_profit_threshold
        8 + // max_gas_fee
        2 + // protocol_fee_bps
        8 + // min_route_amount
        1 + // max_route_steps
        1 + // max_complexity_score
        1 + // max_risk_score
        8 + // max_daily_volume_per_user
        1 + // emergency_stop
        4 + 6 * 1 + // dex_emergency_stops (Vec<Dex>)
        8 + // created_at
        8 + // updated_at
        8 * 8; // reserved

    /// 检查是否支持指定的DEX
    pub fn is_dex_supported(&self, dex: &Dex) -> bool {
        self.supported_dexes.contains(dex) && !self.dex_emergency_stops.contains(dex)
    }

    /// 检查是否可以执行路由
    pub fn can_execute_route(&self, amount: u64, dexes: &[Dex]) -> Result<()> {
        // 检查全局停止
        if self.emergency_stop {
            return Err(crate::error::RouteError::GlobalEmergencyStop.into());
        }

        // 检查金额限制
        if amount > self.max_route_amount {
            return Err(crate::error::RouteError::AmountValidationFailed.into());
        }

        // 检查DEX支持和停止状态
        for dex in dexes {
            if !self.is_dex_supported(dex) {
                return Err(crate::error::RouteError::DexEmergencyStop.into());
            }
        }

        Ok(())
    }

    /// 检查闪电贷是否可用
    pub fn can_use_flash_loan(&self, amount: u64) -> Result<()> {
        if self.emergency_stop {
            return Err(crate::error::RouteError::GlobalEmergencyStop.into());
        }

        if amount > self.max_flash_loan_amount {
            return Err(crate::error::RouteError::FlashLoanAmountExceeded.into());
        }

        Ok(())
    }

    /// 计算协议费用
    pub fn calculate_protocol_fee(&self, amount: u64) -> Result<u64> {
        amount
            .checked_mul(self.protocol_fee_bps as u64)
            .ok_or(crate::error::RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(crate::error::RouteError::DivisionByZero.into())
    }

    /// 更新时间戳
    pub fn touch(&mut self) -> Result<()> {
        self.updated_at = Clock::get()?.unix_timestamp;
        Ok(())
    }

    /// 添加DEX到紧急停止列表
    pub fn add_dex_emergency_stop(&mut self, dex: Dex) {
        if !self.dex_emergency_stops.contains(&dex) {
            self.dex_emergency_stops.push(dex);
        }
    }

    /// 从紧急停止列表移除DEX
    pub fn remove_dex_emergency_stop(&mut self, dex: &Dex) {
        self.dex_emergency_stops.retain(|d| d != dex);
    }
}

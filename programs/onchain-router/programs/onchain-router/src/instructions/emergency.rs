//! 紧急控制指令
//! 
//! 处理紧急停止和恢复操作

use anchor_lang::prelude::*;
use crate::state::RouterConfig;
use crate::routing::types::Dex;

/// 紧急停止账户结构
#[derive(Accounts)]
pub struct EmergencyStop<'info> {
    #[account(
        mut,
        constraint = admin.key() == config.admin @ crate::error::RouteError::PermissionDenied
    )]
    pub admin: Signer<'info>,
    
    #[account(
        mut,
        seeds = [b"config"],
        bump
    )]
    pub config: Account<'info, RouterConfig>,
}

/// 紧急停止处理器
pub fn emergency_stop_handler(
    ctx: Context<EmergencyStop>,
    stop_global: bool,
    stop_dexes: Vec<Dex>,
) -> Result<()> {
    msg!("紧急停止操作 - 管理员: {}", ctx.accounts.admin.key());
    
    let config = &mut ctx.accounts.config;
    
    // 设置全局紧急停止
    if stop_global {
        config.emergency_stop = true;
        msg!("全局紧急停止已启动");
    }
    
    // 设置DEX特定紧急停止
    for dex in stop_dexes {
        if !config.dex_emergency_stops.contains(&dex) {
            config.dex_emergency_stops.push(dex.clone());
            msg!("DEX紧急停止已启动: {:?}", dex);
        }
    }
    
    // 更新时间戳
    config.updated_at = Clock::get()?.unix_timestamp;
    
    // 发出紧急停止事件
    emit!(EmergencyStopEvent {
        admin: ctx.accounts.admin.key(),
        global_stop: config.emergency_stop,
        stopped_dexes: config.dex_emergency_stops.clone(),
        timestamp: Clock::get()?.unix_timestamp,
    });
    
    Ok(())
}

/// 恢复操作账户结构
#[derive(Accounts)]
pub struct EmergencyResume<'info> {
    #[account(
        mut,
        constraint = admin.key() == config.admin @ crate::error::RouteError::PermissionDenied
    )]
    pub admin: Signer<'info>,
    
    #[account(
        mut,
        seeds = [b"config"],
        bump
    )]
    pub config: Account<'info, RouterConfig>,
}

/// 紧急恢复处理器
pub fn emergency_resume_handler(
    ctx: Context<EmergencyResume>,
    resume_global: bool,
    resume_dexes: Vec<Dex>,
) -> Result<()> {
    msg!("紧急恢复操作 - 管理员: {}", ctx.accounts.admin.key());
    
    let config = &mut ctx.accounts.config;
    
    // 恢复全局操作
    if resume_global {
        config.emergency_stop = false;
        msg!("全局紧急停止已恢复");
    }
    
    // 恢复特定DEX操作
    for dex in &resume_dexes {
        config.dex_emergency_stops.retain(|d| d != dex);
        msg!("DEX操作已恢复: {:?}", dex);
    }
    
    // 更新时间戳
    config.updated_at = Clock::get()?.unix_timestamp;
    
    // 发出恢复事件
    emit!(EmergencyResumeEvent {
        admin: ctx.accounts.admin.key(),
        global_resumed: resume_global,
        resumed_dexes: resume_dexes,
        timestamp: Clock::get()?.unix_timestamp,
    });
    
    Ok(())
}

/// 紧急停止事件
#[event]
pub struct EmergencyStopEvent {
    pub admin: Pubkey,
    pub global_stop: bool,
    pub stopped_dexes: Vec<Dex>,
    pub timestamp: i64,
}

/// 紧急恢复事件
#[event]
pub struct EmergencyResumeEvent {
    pub admin: Pubkey,
    pub global_resumed: bool,
    pub resumed_dexes: Vec<Dex>,
    pub timestamp: i64,
}
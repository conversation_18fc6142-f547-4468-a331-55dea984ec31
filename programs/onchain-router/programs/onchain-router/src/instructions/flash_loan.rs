//! 闪电贷指令
//!
//! 处理闪电贷路由的执行

use anchor_lang::prelude::*;
use anchor_spl::token::{Token, TokenAccount};
use anchor_spl::token_interface::{Mint, TokenInterface};
use crate::state::{RouterConfig, UserPosition};
use crate::routing::types::FlashLoanRouteConfig;
use crate::processor::RouteProcessor;
use crate::arbitrage::{
    ArbitrageConfig, ArbitrageCalculator, RiskManager,
    OnchainArbitragePath, ArbitrageStage, ArbitrageExecutedEvent, ArbitrageFailedEvent
};
use crate::flash_loan::create_flash_loan_service;
use crate::routing::RouteResult;

/// 闪电贷路由的账户结构
#[derive(Accounts)]
pub struct FlashLoanRouteAccounts<'info> {
    #[account(mut)]
    pub user: Signer<'info>,

    #[account(
        seeds = [b"config"],
        bump,
        constraint = !config.emergency_stop @ crate::error::RouteError::GlobalEmergencyStop
    )]
    pub config: Account<'info, RouterConfig>,

    #[account(
        mut,
        seeds = [b"position", user.key().as_ref()],
        bump,
        constraint = !user_position.is_suspended @ crate::error::RouteError::UserSuspended
    )]
    pub user_position: Account<'info, UserPosition>,

    /// 闪电贷代币账户（临时持有闪电贷资金）
    #[account(
        mut,
        token::authority = user
    )]
    pub flash_loan_token_account: Account<'info, TokenAccount>,

    /// 闪电贷代币mint
    pub flash_loan_mint: InterfaceAccount<'info, Mint>,

    /// 抵押品代币账户（如果需要）
    #[account(
        mut,
        token::authority = user
    )]
    pub collateral_token_account: Option<Account<'info, TokenAccount>>,

    /// 抵押品代币mint（如果需要）
    pub collateral_mint: Option<InterfaceAccount<'info, Mint>>,

    /// Token程序
    pub token_program: Program<'info, Token>,

    /// Token2022程序（可选）
    pub token_2022_program: Option<Interface<'info, TokenInterface>>,

    /// 系统程序
    pub system_program: Program<'info, System>,

    /// 闪电贷提供者程序
    /// CHECK: 由闪电贷适配器验证
    pub flash_loan_provider: UncheckedAccount<'info>,

    /// 闪电贷池账户
    /// CHECK: 由闪电贷适配器验证
    pub flash_loan_pool: UncheckedAccount<'info>,
}

/// 闪电贷路由处理器
pub fn flash_loan_route_handler<'a>(
    ctx: Context<'_, '_, 'a, 'a, FlashLoanRouteAccounts<'a>>,
    flash_loan_config: FlashLoanRouteConfig,
    order_id: u64,
) -> Result<()> {
    msg!("开始执行闪电贷路由 - 用户: {}, 订单ID: {}", ctx.accounts.user.key(), order_id);

    // 1. 验证闪电贷配置和安全性
    validate_flash_loan_security(&ctx.accounts, &flash_loan_config)?;

    // 2. 验证闪电贷金额不超过配置限制
    if flash_loan_config.flash_loan.amount > ctx.accounts.config.max_flash_loan_amount {
        return Err(crate::error::RouteError::FlashLoanAmountExceeded.into());
    }

    // 3. 验证用户风险等级
    if ctx.accounts.user_position.risk_level > ctx.accounts.config.max_risk_score {
        return Err(crate::error::RouteError::RiskScoreTooHigh.into());
    }

    // 4. 验证闪电贷代币mint
    if flash_loan_config.flash_loan.collateral_mint.is_some() {
        if ctx.accounts.collateral_token_account.is_none() || ctx.accounts.collateral_mint.is_none() {
            return Err(crate::error::RouteError::MissingCollateralAccount.into());
        }
    }

    // 2. 记录执行前状态
    let start_time = Clock::get()?.unix_timestamp;

    // 3. 执行闪电贷路由
    let result = RouteProcessor::execute_flash_loan_route(
        ctx.accounts,
        flash_loan_config.clone(),
        ctx.remaining_accounts,
        order_id,
    )?;

    // 4. 验证最小利润
    if result < flash_loan_config.expected_profit {
        msg!("警告: 实际利润 {} 低于预期 {}", result, flash_loan_config.expected_profit);
    }

    // 5. 更新用户统计
    let user_position = &mut ctx.accounts.user_position;
    user_position.total_volume += flash_loan_config.flash_loan.amount;
    user_position.successful_routes += 1;
    user_position.total_profit += result;
    user_position.last_activity = Clock::get()?.unix_timestamp;

    // 6. 发出闪电贷执行完成事件
    emit!(FlashLoanExecutedEvent {
        user: ctx.accounts.user.key(),
        order_id,
        flash_loan_amount: flash_loan_config.flash_loan.amount,
        profit: result,
        steps: flash_loan_config.arbitrage_routes.len() as u8,
        execution_time: Clock::get()?.unix_timestamp - start_time,
        timestamp: Clock::get()?.unix_timestamp,
    });

    msg!("闪电贷路由执行完成 - 闪电贷金额: {}, 利润: {}",
        flash_loan_config.flash_loan.amount, result);
    Ok(())
}

/// 完整的零本金套利执行指令
pub fn execute_flash_loan_arbitrage<'a>(
    ctx: Context<'_, '_, 'a, 'a, FlashLoanArbitrageAccounts<'a>>,
    path: OnchainArbitragePath,
    flash_amount: u64,
) -> Result<()> {
    msg!("开始执行零本金闪电贷套利 - 用户: {}, 闪电贷金额: {}",
        ctx.accounts.user.key(), flash_amount);

    let start_time = Clock::get()?.unix_timestamp;
    let mut current_stage = ArbitrageStage::Initialization;
    msg!("套利阶段: {:?}", current_stage);

    // 1. 初始化套利系统组件
    let arbitrage_config = ArbitrageConfig {
        min_profit_threshold: ctx.accounts.config.min_profit_threshold,
        max_flash_loan_amount: ctx.accounts.config.max_flash_loan_amount,
        max_slippage_bps: 300,
        max_gas_fee: 50000,
        enable_dynamic_risk: true,
        protocol_fee_bps: 50,
    };

    let calculator = ArbitrageCalculator::new(arbitrage_config.clone());
    let mut risk_manager = RiskManager::new(arbitrage_config.clone());

    // 2. 验证套利路径
    path.validate()?;

    // 3. 风险评估阶段
    current_stage = ArbitrageStage::RiskAssessment;
    msg!("套利阶段: {:?}", current_stage);

    // 创建模拟市场条件
    let market_conditions = crate::arbitrage::MarketConditions {
        network_congestion: 30,
        avg_gas_price: 100,
        volatility: 40,
        liquidity_level: 70,
    };

    // 解析套利路径
    let routes = deserialize_arbitrage_routes(&path.route_data)?;
    let arbitrage_path = crate::arbitrage::ArbitragePath {
        routes: routes.clone(),
        estimated_profit: path.estimated_profit,
        risk_score: 0, // 将在风险评估中计算
        success_probability: 0.85,
        priority: 100,
    };

    // 执行风险评估
    let risk_assessment = risk_manager.assess_arbitrage_risk(
        &arbitrage_path,
        flash_amount,
        &market_conditions,
    )?;

    if !risk_assessment.recommended {
        msg!("风险评估不通过 - 风险评分: {}, 警告: {:?}",
            risk_assessment.overall_risk_score, risk_assessment.warnings);

        emit!(ArbitrageFailedEvent {
            user: ctx.accounts.user.key(),
            path_hash: calculate_path_hash(&path),
            error_code: crate::error::RouteError::RiskScoreTooHigh as u32,
            failed_stage: current_stage,
            timestamp: Clock::get()?.unix_timestamp,
        });

        return Err(crate::error::RouteError::RiskScoreTooHigh.into());
    }

    // 4. 闪电贷申请阶段
    current_stage = ArbitrageStage::FlashLoanRequest;
    msg!("套利阶段: {:?}", current_stage);

    let mut flash_loan_service = create_flash_loan_service();
    let mint = ctx.accounts.flash_loan_mint.key();

    // 利润预估
    let flash_loan_provider = flash_loan_service.get_best_provider(flash_amount, &mint)?;
    let profit_estimate = calculator.estimate_profit(
        &routes,
        flash_amount,
        flash_loan_provider.as_ref(),
    )?;

    msg!("利润预估 - 净利润: {}, 成功概率: {:.2}",
        profit_estimate.net_profit, profit_estimate.success_probability);

    // 检查预估利润是否足够
    if profit_estimate.net_profit < arbitrage_config.min_profit_threshold {
        msg!("预估利润不足 - 预估: {}, 要求: {}",
            profit_estimate.net_profit, arbitrage_config.min_profit_threshold);

        emit!(ArbitrageFailedEvent {
            user: ctx.accounts.user.key(),
            path_hash: calculate_path_hash(&path),
            error_code: crate::error::RouteError::InsufficientProfit as u32,
            failed_stage: current_stage,
            timestamp: Clock::get()?.unix_timestamp,
        });

        return Err(crate::error::RouteError::InsufficientProfit.into());
    }

    // 5. 路由执行阶段
    current_stage = ArbitrageStage::RouteExecution;
    msg!("套利阶段: {:?}", current_stage);

    // 构建账户信息数组
    let mut account_infos = Vec::new();
    account_infos.push(ctx.accounts.user.to_account_info());
    account_infos.push(ctx.accounts.flash_loan_token_account.to_account_info());
    account_infos.push(ctx.accounts.flash_loan_pool.to_account_info());
    account_infos.push(ctx.accounts.flash_loan_mint.to_account_info());
    account_infos.push(ctx.accounts.token_program.to_account_info());

    // 添加剩余账户
    for account in ctx.remaining_accounts {
        account_infos.push(account.clone());
    }

    // 执行闪电贷套利
    let callback_data = serialize_arbitrage_callback(&routes)?;
    let execution_result = flash_loan_service.execute_flash_loan_arbitrage(
        &account_infos,
        flash_amount,
        &mint,
        &callback_data,
    )?;

    // 6. 利润计算阶段
    current_stage = ArbitrageStage::ProfitCalculation;
    msg!("套利阶段: {:?}", current_stage);

    let execution_time_ms = (Clock::get()?.unix_timestamp - start_time) as u64 * 1000;
    let flash_loan_fee = profit_estimate.fee_breakdown.flash_loan_fee;

    // 计算实际利润
    let route_result = RouteResult {
        amount_out: execution_result,
        amount_in: flash_amount,
        gas_used: profit_estimate.fee_breakdown.gas_fee / 100, // 估算的CU使用量
        actual_slippage_bps: 0, // 实际应该从执行结果中获取
        net_profit: 0, // 将被重新计算
        expected_amount: Some(profit_estimate.estimated_output),
        steps_executed: 0,
    };

    let arbitrage_result = calculator.calculate_actual_profit(
        &route_result,
        flash_amount,
        flash_loan_fee,
        execution_time_ms,
    )?;

    // 7. 闪电贷偿还阶段
    current_stage = ArbitrageStage::FlashLoanRepayment;
    msg!("套利阶段: {:?}", current_stage);

    // 验证能够偿还闪电贷
    let repayment_amount = flash_amount + flash_loan_fee;
    if arbitrage_result.route_result.amount_out < repayment_amount {
        msg!("闪电贷偿还失败 - 输出: {}, 需要: {}",
            arbitrage_result.route_result.amount_out, repayment_amount);

        emit!(ArbitrageFailedEvent {
            user: ctx.accounts.user.key(),
            path_hash: calculate_path_hash(&path),
            error_code: crate::error::RouteError::FlashLoanRepaymentFailed as u32,
            failed_stage: current_stage,
            timestamp: Clock::get()?.unix_timestamp,
        });

        return Err(crate::error::RouteError::FlashLoanRepaymentFailed.into());
    }

    // 8. 利润分配阶段
    current_stage = ArbitrageStage::ProfitDistribution;
    msg!("套利阶段: {:?}", current_stage);

    let _profit_distribution = calculator.calculate_profit_distribution(
        arbitrage_result.net_profit,
        0, // 零本金套利，无用户投入
        flash_amount,
    )?;

    // 更新用户统计
    let user_position = &mut ctx.accounts.user_position;
    user_position.total_volume += flash_amount;
    user_position.successful_routes += 1;
    user_position.total_profit += arbitrage_result.net_profit;
    user_position.last_activity = Clock::get()?.unix_timestamp;

    // 更新风险管理器统计
    risk_manager.update_execution_stats(
        true,
        arbitrage_result.net_profit as i64,
        execution_time_ms,
        flash_amount,
    );

    // 发出套利执行成功事件
    emit!(ArbitrageExecutedEvent {
        user: ctx.accounts.user.key(),
        path_hash: calculate_path_hash(&path),
        flash_loan_amount: flash_amount,
        net_profit: arbitrage_result.net_profit,
        risk_score: risk_assessment.overall_risk_score,
        execution_time_ms,
        timestamp: Clock::get()?.unix_timestamp,
    });

    msg!("零本金套利执行成功 - 净利润: {}, 执行时间: {}ms, 风险评分: {}",
        arbitrage_result.net_profit, execution_time_ms, risk_assessment.overall_risk_score);

    Ok(())
}

/// 零本金套利账户结构
#[derive(Accounts)]
pub struct FlashLoanArbitrageAccounts<'info> {
    #[account(mut)]
    pub user: Signer<'info>,

    #[account(
        seeds = [b"config"],
        bump,
        constraint = !config.emergency_stop @ crate::error::RouteError::GlobalEmergencyStop
    )]
    pub config: Account<'info, RouterConfig>,

    #[account(
        mut,
        seeds = [b"position", user.key().as_ref()],
        bump,
        constraint = !user_position.is_suspended @ crate::error::RouteError::UserSuspended
    )]
    pub user_position: Account<'info, UserPosition>,

    /// 闪电贷代币账户
    #[account(
        mut,
        token::authority = user
    )]
    pub flash_loan_token_account: Account<'info, TokenAccount>,

    /// 闪电贷代币mint
    pub flash_loan_mint: InterfaceAccount<'info, Mint>,

    /// 用户利润接收账户
    #[account(
        mut,
        token::authority = user,
        token::mint = flash_loan_mint
    )]
    pub user_profit_account: Account<'info, TokenAccount>,

    /// 协议费用接收账户
    #[account(
        mut,
        token::mint = flash_loan_mint
    )]
    pub protocol_fee_account: Account<'info, TokenAccount>,

    /// Token程序
    pub token_program: Program<'info, Token>,

    /// Token2022程序（可选）
    pub token_2022_program: Option<Interface<'info, TokenInterface>>,

    /// 系统程序
    pub system_program: Program<'info, System>,

    /// 闪电贷提供者程序
    /// CHECK: 由闪电贷适配器验证
    pub flash_loan_provider: UncheckedAccount<'info>,

    /// 闪电贷池账户
    /// CHECK: 由闪电贷适配器验证
    pub flash_loan_pool: UncheckedAccount<'info>,
}

/// 增强版闪电贷路由处理器（使用新的闪电贷服务）
pub fn enhanced_flash_loan_route_handler<'a>(
    ctx: Context<'_, '_, 'a, 'a, FlashLoanRouteAccounts<'a>>,
    flash_loan_config: FlashLoanRouteConfig,
    order_id: u64,
    callback_data: Vec<u8>,
) -> Result<()> {
    msg!("开始执行增强版闪电贷路由 - 用户: {}, 订单ID: {}", ctx.accounts.user.key(), order_id);

    // 1. 验证闪电贷配置和安全性
    validate_flash_loan_security(&ctx.accounts, &flash_loan_config)?;

    // 2. 验证闪电贷金额不超过配置限制
    if flash_loan_config.flash_loan.amount > ctx.accounts.config.max_flash_loan_amount {
        return Err(crate::error::RouteError::FlashLoanAmountExceeded.into());
    }

    // 3. 验证用户风险等级
    if ctx.accounts.user_position.risk_level > ctx.accounts.config.max_risk_score {
        return Err(crate::error::RouteError::RiskScoreTooHigh.into());
    }

    // 4. 创建闪电贷服务
    let mut flash_loan_service = create_flash_loan_service();

    // 5. 根据配置调整服务参数
    flash_loan_service.get_manager_mut().set_max_fee_bps(
        flash_loan_config.flash_loan.max_fee_bps
    );

    // 6. 记录执行前状态
    let start_time = Clock::get()?.unix_timestamp;

    // 7. 预估利润和选择最优提供者
    let mint = ctx.accounts.flash_loan_mint.key();
    let profit_estimate = flash_loan_service.estimate_arbitrage_profit(
        flash_loan_config.flash_loan.amount,
        &mint,
        &callback_data,
    )?;

    msg!("利润预估 - 净利润: {}, 成功概率: {:.2}",
        profit_estimate.net_profit, profit_estimate.success_probability);

    // 8. 检查利润预估是否满足要求
    if profit_estimate.net_profit < flash_loan_config.expected_profit {
        msg!("预估利润不足 - 预估: {}, 要求: {}",
            profit_estimate.net_profit, flash_loan_config.expected_profit);
        return Err(crate::error::RouteError::InsufficientProfit.into());
    }

    // 9. 构建账户信息数组
    let mut account_infos = Vec::new();
    account_infos.push(ctx.accounts.user.to_account_info());
    account_infos.push(ctx.accounts.flash_loan_token_account.to_account_info());
    account_infos.push(ctx.accounts.flash_loan_pool.to_account_info());
    account_infos.push(ctx.accounts.flash_loan_mint.to_account_info());
    account_infos.push(ctx.accounts.token_program.to_account_info());

    // 添加剩余账户
    for account in ctx.remaining_accounts {
        account_infos.push(account.clone());
    }

    // 10. 执行闪电贷套利
    let result = flash_loan_service.execute_flash_loan_arbitrage(
        &account_infos,
        flash_loan_config.flash_loan.amount,
        &mint,
        &callback_data,
    )?;

    // 11. 验证实际利润
    if result < flash_loan_config.expected_profit {
        msg!("警告: 实际利润 {} 低于预期 {}", result, flash_loan_config.expected_profit);
    }

    // 12. 更新用户统计
    let user_position = &mut ctx.accounts.user_position;
    user_position.total_volume += flash_loan_config.flash_loan.amount;
    user_position.successful_routes += 1;
    user_position.total_profit += result;
    user_position.last_activity = Clock::get()?.unix_timestamp;

    let execution_time = Clock::get()?.unix_timestamp - start_time;

    // 13. 发出增强版闪电贷执行完成事件
    emit!(EnhancedFlashLoanExecutedEvent {
        user: ctx.accounts.user.key(),
        order_id,
        flash_loan_amount: flash_loan_config.flash_loan.amount,
        provider: profit_estimate.flash_loan_fee, // 临时使用费用字段
        estimated_profit: profit_estimate.net_profit,
        actual_profit: result,
        execution_time,
        success_probability: (profit_estimate.success_probability * 100.0) as u8,
        timestamp: Clock::get()?.unix_timestamp,
    });

    msg!("增强版闪电贷路由执行完成 - 闪电贷金额: {}, 利润: {}, 执行时间: {}s",
        flash_loan_config.flash_loan.amount, result, execution_time);
    Ok(())
}

/// 闪电贷执行成功事件
#[event]
pub struct FlashLoanExecutedEvent {
    pub user: Pubkey,
    pub order_id: u64,
    pub flash_loan_amount: u64,
    pub profit: u64,
    pub steps: u8,
    pub execution_time: i64,
    pub timestamp: i64,
}

/// 闪电贷执行失败事件
#[event]
pub struct FlashLoanFailedEvent {
    pub user: Pubkey,
    pub order_id: u64,
    pub error_code: u32,
    pub flash_loan_amount: u64,
    pub failed_step: u8,
    pub timestamp: i64,
}

/// 增强版闪电贷执行成功事件
#[event]
pub struct EnhancedFlashLoanExecutedEvent {
    pub user: Pubkey,
    pub order_id: u64,
    pub flash_loan_amount: u64,
    pub provider: u64, // 临时使用u64，实际应该是字符串
    pub estimated_profit: u64,
    pub actual_profit: u64,
    pub execution_time: i64,
    pub success_probability: u8,
    pub timestamp: i64,
}

/// 验证闪电贷安全性
fn validate_flash_loan_security(
    accounts: &FlashLoanRouteAccounts,
    flash_loan_config: &FlashLoanRouteConfig,
) -> Result<()> {
    // 验证闪电贷提供者在白名单中
    if !is_flash_loan_provider_approved(&accounts.config, &accounts.flash_loan_provider.key()) {
        return Err(crate::error::RouteError::UnauthorizedFlashLoanProvider.into());
    }

    // 验证套利路由的有效性
    for route in &flash_loan_config.arbitrage_routes {
        route.validate()?;
    }

    // 验证预期利润的合理性
    if flash_loan_config.expected_profit < accounts.config.min_profit_threshold {
        return Err(crate::error::RouteError::ProfitBelowThreshold.into());
    }

    // 验证Gas费用限制
    if flash_loan_config.max_gas_fee > accounts.config.max_gas_fee {
        return Err(crate::error::RouteError::GasFeeTooHigh.into());
    }

    // 验证闪电贷费率的合理性
    if flash_loan_config.flash_loan.max_fee_bps > 1000 { // 最大10%费率
        return Err(crate::error::RouteError::FlashLoanFeeTooHigh.into());
    }

    Ok(())
}

/// 检查闪电贷提供者是否在白名单中
fn is_flash_loan_provider_approved(config: &RouterConfig, provider: &Pubkey) -> bool {
    // 这里应该检查配置中的闪电贷提供者白名单
    // 暂时简单实现，在实际项目中应该有完整的白名单机制

    // 一些知名的闪电贷提供者程序ID（示例）
    let approved_providers = [
        // 这里应该是实际的闪电贷提供者程序ID
        // 例如 Solend, Mango, Aave 等的程序ID
    ];

    approved_providers.contains(provider) || *provider == config.admin
}

/// 反序列化套利路径数据
fn deserialize_arbitrage_routes(route_data: &[u8]) -> Result<Vec<crate::routing::types::Route>> {
    // 简化实现，实际应该使用完整的序列化/反序列化
    if route_data.is_empty() {
        return Err(crate::error::RouteError::InvalidRouteConfig.into());
    }

    // 暂时返回一个示例路由，实际应该从字节数据解析
    let route = crate::routing::types::Route {
        dex: crate::routing::types::Dex::RaydiumClmm,
        input_mint: Pubkey::new_unique(),
        output_mint: Pubkey::new_unique(),
        swap_data: vec![],
        min_amount_out: 0,
    };

    Ok(vec![route])
}

/// 序列化套利回调数据
fn serialize_arbitrage_callback(routes: &[crate::routing::types::Route]) -> Result<Vec<u8>> {
    // 简化实现，实际应该使用完整的序列化
    let mut data = Vec::new();
    data.push(routes.len() as u8);

    for route in routes {
        // 添加路由标识符
        data.push(route.dex as u8);
        data.extend_from_slice(&route.input_mint.to_bytes());
        data.extend_from_slice(&route.output_mint.to_bytes());
    }

    Ok(data)
}

/// 计算路径哈希
fn calculate_path_hash(path: &OnchainArbitragePath) -> [u8; 32] {
    use anchor_lang::solana_program::hash::hash;

    let mut data = Vec::new();
    data.extend_from_slice(&path.route_data);
    data.extend_from_slice(&path.estimated_profit.to_le_bytes());
    data.extend_from_slice(&path.max_slippage_bps.to_le_bytes());
    data.extend_from_slice(&path.timeout_seconds.to_le_bytes());

    let hash_result = hash(&data);
    hash_result.to_bytes()
}

//! 初始化指令
//!
//! 处理路由器配置的初始化

use anchor_lang::prelude::*;
use crate::state::{RouterConfig, UserPosition};
use crate::routing::types::Dex;

/// 初始化路由器配置的账户结构
#[derive(Accounts)]
pub struct InitializeConfig<'info> {
    #[account(mut)]
    pub admin: Signer<'info>,

    #[account(
        init,
        payer = admin,
        space = RouterConfig::LEN,
        seeds = [b"config"],
        bump
    )]
    pub config: Account<'info, RouterConfig>,

    pub system_program: Program<'info, System>,
}

/// 初始化用户位置的账户结构
#[derive(Accounts)]
pub struct InitializeUserPosition<'info> {
    #[account(mut)]
    pub user: Signer<'info>,

    #[account(
        init,
        payer = user,
        space = UserPosition::LEN,
        seeds = [b"position", user.key().as_ref()],
        bump
    )]
    pub user_position: Account<'info, UserPosition>,

    pub system_program: Program<'info, System>,
}

/// 配置参数
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ConfigArgs {
    /// 支持的DEX列表
    pub supported_dexes: Vec<Dex>,
    /// 最大路由金额
    pub max_route_amount: u64,
    /// 最大闪电贷金额
    pub max_flash_loan_amount: u64,
    /// 最大滑点（基点）
    pub max_slippage_bps: u16,
    /// 最小利润阈值
    pub min_profit_threshold: u64,
    /// 最大Gas费用
    pub max_gas_fee: u64,
    /// 协议费率（基点）
    pub protocol_fee_bps: u16,
}

/// 初始化配置处理器
pub fn initialize_config_handler(
    ctx: Context<InitializeConfig>,
    config_data: ConfigArgs,
) -> Result<()> {
    msg!("初始化路由器配置");

    let config = &mut ctx.accounts.config;

    // 设置管理员
    config.admin = ctx.accounts.admin.key();

    // 设置基本参数
    config.supported_dexes = config_data.supported_dexes;
    config.max_route_amount = config_data.max_route_amount;
    config.max_flash_loan_amount = config_data.max_flash_loan_amount;
    config.max_slippage_bps = config_data.max_slippage_bps;
    config.min_profit_threshold = config_data.min_profit_threshold;
    config.max_gas_fee = config_data.max_gas_fee;
    config.protocol_fee_bps = config_data.protocol_fee_bps;

    // 设置新加字段的默认值
    config.min_route_amount = 1000; // 0.001 USDC
    config.max_route_steps = 6;
    config.max_complexity_score = 24;
    config.max_risk_score = 80;
    config.max_daily_volume_per_user = 100_000 * 10u64.pow(6); // 100K USDC

    // 初始化控制状态
    config.emergency_stop = false;
    config.dex_emergency_stops = Vec::new();

    // 设置创建时间
    config.created_at = Clock::get()?.unix_timestamp;
    config.updated_at = Clock::get()?.unix_timestamp;

    msg!("路由器配置初始化完成 - 管理员: {}", config.admin);
    Ok(())
}

/// 初始化用户位置处理器
pub fn initialize_user_position_handler(ctx: Context<InitializeUserPosition>) -> Result<()> {
    msg!("初始化用户位置 - 用户: {}", ctx.accounts.user.key());

    let user_position = &mut ctx.accounts.user_position;

    // 设置用户基本信息
    user_position.user = ctx.accounts.user.key();
    user_position.is_suspended = false;
    user_position.risk_level = 1; // 默认风险等级

    // 初始化统计数据
    user_position.total_volume = 0;
    user_position.total_profit = 0;
    user_position.total_loss = 0;
    user_position.successful_routes = 0;
    user_position.failed_routes = 0;
    user_position.total_successful_trades = 0;
    user_position.total_failed_trades = 0;
    user_position.daily_volume_used = 0;
    user_position.last_activity_time = Clock::get()?.unix_timestamp;
    user_position.last_activity_deadline = None;

    // 设置时间戳
    user_position.created_at = Clock::get()?.unix_timestamp;
    user_position.last_activity = Clock::get()?.unix_timestamp;

    msg!("用户位置初始化完成");
    Ok(())
}

impl ConfigArgs {
    /// 验证配置参数的有效性
    pub fn validate(&self) -> Result<()> {
        // 检查DEX列表不为空
        if self.supported_dexes.is_empty() {
            return Err(crate::error::RouteError::InvalidRouteConfig.into());
        }

        // 检查金额限制合理性
        if self.max_route_amount == 0 {
            return Err(crate::error::RouteError::AmountValidationFailed.into());
        }

        if self.max_flash_loan_amount == 0 {
            return Err(crate::error::RouteError::FlashLoanAmountExceeded.into());
        }

        // 检查滑点限制（不能超过100%）
        if self.max_slippage_bps > 10000 {
            return Err(crate::error::RouteError::SlippageTooHigh.into());
        }

        // 检查协议费率合理性（不能超过5%）
        if self.protocol_fee_bps > 500 {
            return Err(crate::error::RouteError::InvalidRouteConfig.into());
        }

        Ok(())
    }

    /// 创建默认配置
    pub fn default() -> Self {
        Self {
            supported_dexes: vec![
                Dex::RaydiumClmm,
                Dex::RaydiumCpmm,
                Dex::MeteoraAmm,
                Dex::MeteoraLb,
                Dex::Orca,
                Dex::PumpSwap,
            ],
            max_route_amount: 1_000_000 * 10u64.pow(6), // 1M USDC
            max_flash_loan_amount: 10_000_000 * 10u64.pow(6), // 10M USDC
            max_slippage_bps: 300, // 3%
            min_profit_threshold: 1000, // 最小利润0.001 USDC
            max_gas_fee: 10_000, // 最大Gas费用
            protocol_fee_bps: 30, // 0.3%协议费
        }
    }
}

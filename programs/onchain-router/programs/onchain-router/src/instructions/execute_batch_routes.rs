//! 批量路由执行指令
//!
//! 处理并行执行多个独立路由操作

use anchor_lang::prelude::*;
use anchor_spl::token::{Token};
use anchor_spl::token_interface::{TokenInterface};
use crate::state::{RouterConfig, UserPosition};
use crate::routing::types::BatchRouteConfig;
use crate::processor::*;

/// 执行批量路由的账户结构
#[derive(Accounts)]
pub struct ExecuteBatchRoutesAccounts<'info> {
    #[account(mut)]
    pub user: Signer<'info>,

    #[account(
        seeds = [b"config"],
        bump,
        constraint = !config.emergency_stop @ crate::error::RouteError::GlobalEmergencyStop
    )]
    pub config: Account<'info, RouterConfig>,

    #[account(
        mut,
        seeds = [b"position", user.key().as_ref()],
        bump,
        constraint = !user_position.is_suspended @ crate::error::RouteError::UserSuspended
    )]
    pub user_position: Account<'info, UserPosition>,

    /// Token程序
    pub token_program: Program<'info, Token>,

    /// Token2022程序（可选）
    pub token_2022_program: Option<Interface<'info, TokenInterface>>,

    /// 系统程序
    pub system_program: Program<'info, System>,

    // 注意：批量路由的具体Token账户将通过remaining_accounts传递
    // 因为每个路由可能涉及不同的代币对
}

/// 执行批量路由处理器
pub fn execute_batch_routes_handler<'a>(
    ctx: Context<'_, '_, 'a, 'a, ExecuteBatchRoutesAccounts<'a>>,
    batch_config: BatchRouteConfig,
    order_id: u64,
) -> Result<()> {
    msg!("开始执行批量路由 - 用户: {}, 订单ID: {}, 路由数量: {}",
        ctx.accounts.user.key(), order_id, batch_config.routes.len());

    // 1. 验证批量配置
    validate_batch_config(&batch_config, &ctx.accounts)?;

    // 2. 验证剩余账户数量（每个路由至少需要4个账户：源账户、目标账户、源mint、目标mint）
    let expected_accounts = batch_config.routes.len() * 4;
    if ctx.remaining_accounts.len() < expected_accounts {
        return Err(crate::error::RouteError::InvalidRouteConfig.into());
    }

    // 3. 记录执行前状态
    let start_time = Clock::get()?.unix_timestamp;
    let mut total_volume = 0u64;

    // 预先计算总交易量
    for route in &batch_config.routes {
        total_volume = total_volume.saturating_add(route.amount_in);
    }

    // 4. 执行批量路由
    let result = RouteProcessor::execute_batch_routes(
        ctx.accounts,
        batch_config.clone(),
        ctx.remaining_accounts,
        order_id,
    )?;

    // 5. 检查原子性要求
    if batch_config.atomic && result.success_count < result.total_routes {
        return Err(crate::error::RouteError::AtomicExecutionFailed.into());
    }

    // 6. 更新用户统计
    let user_position = &mut ctx.accounts.user_position;
    user_position.total_volume += total_volume;
    user_position.successful_routes += result.success_count as u32;
    user_position.failed_routes += (result.total_routes - result.success_count) as u32;
    user_position.last_activity = Clock::get()?.unix_timestamp;

    // 7. 发出批量路由执行完成事件
    emit!(BatchRoutesExecutedEvent {
        user: ctx.accounts.user.key(),
        order_id,
        total_routes: result.total_routes,
        successful_routes: result.success_count,
        total_input_volume: total_volume,
        total_output: result.total_output,
        atomic_execution: batch_config.atomic,
        execution_time: Clock::get()?.unix_timestamp - start_time,
        timestamp: Clock::get()?.unix_timestamp,
    });

    if result.success_count == result.total_routes {
        msg!("批量路由全部执行成功 - 总输入: {}, 总输出: {}", total_volume, result.total_output);
    } else {
        msg!("批量路由部分执行成功 - 成功: {}/{}, 总输出: {}",
            result.success_count, result.total_routes, result.total_output);
    }

    Ok(())
}

/// 验证批量配置的有效性
fn validate_batch_config(
    batch_config: &BatchRouteConfig,
    _accounts: &ExecuteBatchRoutesAccounts
) -> Result<()> {
    // 验证路由数量限制（最多10个并行路由）
    if batch_config.routes.is_empty() {
        return Err(crate::error::RouteError::EmptyRoutePath.into());
    }

    if batch_config.routes.len() > 10 {
        return Err(crate::error::RouteError::RoutePathTooLong.into());
    }

    // 验证每个路由的有效性
    for (i, route_config) in batch_config.routes.iter().enumerate() {
        route_config.validate().map_err(|_| {
            msg!("批量路由第{}个配置无效", i + 1);
            crate::error::RouteError::InvalidRouteConfig
        })?;
    }

    // 计算总复杂度评分
    let total_complexity: u32 = batch_config.routes.iter()
        .map(|route| route.complexity_score() as u32)
        .sum();

    if total_complexity > 100 {
        return Err(crate::error::RouteError::RouteComplexityTooHigh.into());
    }

    Ok(())
}

/// 批量路由执行成功事件
#[event]
pub struct BatchRoutesExecutedEvent {
    pub user: Pubkey,
    pub order_id: u64,
    pub total_routes: u8,
    pub successful_routes: u8,
    pub total_input_volume: u64,
    pub total_output: u64,
    pub atomic_execution: bool,
    pub execution_time: i64,
    pub timestamp: i64,
}

/// 批量路由执行失败事件
#[event]
pub struct BatchRoutesFailedEvent {
    pub user: Pubkey,
    pub order_id: u64,
    pub error_code: u32,
    pub total_routes: u8,
    pub failed_at_route: u8,
    pub successful_routes: u8,
    pub atomic_execution: bool,
    pub timestamp: i64,
}

/// 验证剩余账户中的Token账户
#[allow(dead_code)]
fn validate_token_accounts_in_remaining(
    remaining_accounts: &[AccountInfo],
    batch_config: &BatchRouteConfig,
) -> Result<()> {
    // 这里可以添加更详细的Token账户验证逻辑
    // 例如验证账户类型、权限、余额等

    let mut account_index = 0;
    for (route_index, _route_config) in batch_config.routes.iter().enumerate() {
        // 每个路由期望4个账户：源账户、目标账户、源mint、目标mint
        if account_index + 4 > remaining_accounts.len() {
            msg!("批量路由第{}个路由的账户数量不足", route_index + 1);
            return Err(crate::error::RouteError::InvalidRouteConfig.into());
        }

        // 这里可以添加更具体的账户验证
        // 例如验证mint地址是否匹配等

        account_index += 4;
    }

    Ok(())
}

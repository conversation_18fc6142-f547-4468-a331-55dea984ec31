//! 数学计算工具
//! 
//! 提供高精度的数学计算函数，避免溢出和精度丢失

use anchor_lang::prelude::*;
use crate::error::RouteError;

/// 安全的乘法运算，检查溢出
pub fn safe_mul(a: u64, b: u64) -> Result<u64> {
    a.checked_mul(b).ok_or(RouteError::MathOverflow.into())
}

/// 安全的除法运算，检查零除
pub fn safe_div(a: u64, b: u64) -> Result<u64> {
    if b == 0 {
        return Err(RouteError::DivisionByZero.into());
    }
    Ok(a / b)
}

/// 安全的加法运算，检查溢出
pub fn safe_add(a: u64, b: u64) -> Result<u64> {
    a.checked_add(b).ok_or(RouteError::MathOverflow.into())
}

/// 安全的减法运算，检查下溢
pub fn safe_sub(a: u64, b: u64) -> Result<u64> {
    a.checked_sub(b).ok_or(RouteError::MathUnderflow.into())
}

/// 计算百分比（基点）
/// 例如：calculate_bps(1000, 300) = 30 (300基点 = 3%)
pub fn calculate_bps(amount: u64, bps: u16) -> Result<u64> {
    safe_mul(amount, bps as u64)
        .and_then(|result| safe_div(result, 10000))
}

/// 计算滑点百分比
pub fn calculate_slippage_bps(expected: u64, actual: u64) -> Result<u16> {
    if expected == 0 {
        return Ok(0);
    }
    
    let diff = if actual >= expected {
        0 // 正滑点（更好的价格）
    } else {
        safe_sub(expected, actual)?
    };
    
    let slippage = safe_mul(diff, 10000)
        .and_then(|result| safe_div(result, expected))?;
    
    // 限制在合理范围内（最大100%）
    Ok((slippage as u16).min(10000))
}

/// 计算复合利率
/// amount * (1 + rate/10000)^periods
pub fn calculate_compound_return(
    principal: u64,
    rate_bps: u16,
    periods: u32,
) -> Result<u64> {
    if periods == 0 {
        return Ok(principal);
    }
    
    let mut result = principal;
    let multiplier = 10000 + rate_bps as u64;
    
    for _ in 0..periods {
        result = safe_mul(result, multiplier)
            .and_then(|r| safe_div(r, 10000))?;
    }
    
    Ok(result)
}

/// 计算最小输出数量（考虑滑点保护）
pub fn calculate_min_amount_out(
    expected_amount: u64,
    max_slippage_bps: u16,
) -> Result<u64> {
    let slippage_amount = calculate_bps(expected_amount, max_slippage_bps)?;
    safe_sub(expected_amount, slippage_amount)
}

/// 计算价格影响
pub fn calculate_price_impact(
    input_amount: u64,
    output_amount: u64,
    expected_rate: u64, // 期望汇率 (scaled by 1e6)
) -> Result<u16> {
    // 实际汇率
    let actual_rate = safe_mul(output_amount, 1_000_000)
        .and_then(|r| safe_div(r, input_amount))?;
    
    // 价格影响 = |实际汇率 - 期望汇率| / 期望汇率 * 10000
    let diff = if actual_rate >= expected_rate {
        safe_sub(actual_rate, expected_rate)?
    } else {
        safe_sub(expected_rate, actual_rate)?
    };
    
    let impact = safe_mul(diff, 10000)
        .and_then(|r| safe_div(r, expected_rate))?;
    
    Ok((impact as u16).min(10000))
}

/// 计算几何平均数（用于多步路由的平均汇率）
pub fn calculate_geometric_mean(rates: &[u64]) -> Result<u64> {
    if rates.is_empty() {
        return Err(RouteError::InvalidRouteConfig.into());
    }
    
    if rates.len() == 1 {
        return Ok(rates[0]);
    }
    
    // 简化实现：使用算术平均数近似
    // 在实际实现中应该使用更精确的几何平均算法
    let sum: u64 = rates.iter().try_fold(0u64, |acc, &rate| {
        safe_add(acc, rate)
    })?;
    
    safe_div(sum, rates.len() as u64)
}

/// 计算流动性效率分数
pub fn calculate_liquidity_efficiency(
    input_amount: u64,
    _output_amount: u64,
    pool_liquidity: u64,
) -> Result<u8> {
    if pool_liquidity == 0 {
        return Ok(0);
    }
    
    // 计算利用率
    let utilization = safe_mul(input_amount, 100)
        .and_then(|r| safe_div(r, pool_liquidity))?;
    
    // 计算效率（简化算法）
    let efficiency = if utilization < 1 {
        90 // 低利用率，高效率
    } else if utilization < 5 {
        80
    } else if utilization < 10 {
        70
    } else if utilization < 20 {
        60
    } else {
        50 // 高利用率，可能有较大价格影响
    };
    
    Ok(efficiency)
}

/// 计算最优分割数量（用于大额交易分割）
pub fn calculate_optimal_split(
    total_amount: u64,
    pool_liquidity: u64,
    max_price_impact_bps: u16,
) -> Result<(u64, u8)> {
    // 基于流动性计算建议的单次交易大小
    let max_single_amount = safe_mul(pool_liquidity, max_price_impact_bps as u64)
        .and_then(|r| safe_div(r, 10000))?;
    
    if total_amount <= max_single_amount {
        return Ok((total_amount, 1)); // 不需要分割
    }
    
    // 计算分割数量
    let splits = safe_div(total_amount, max_single_amount)?
        .checked_add(1)
        .ok_or(RouteError::MathOverflow)?;
    
    let splits = (splits as u8).min(10); // 最多分割10次
    let amount_per_split = safe_div(total_amount, splits as u64)?;
    
    Ok((amount_per_split, splits))
}

/// 计算加权平均价格（TWAP）
pub fn calculate_time_weighted_average_price(
    prices: &[(u64, u64)], // (price, timestamp)
    start_time: u64,
    end_time: u64,
) -> Result<u64> {
    if prices.is_empty() || start_time >= end_time {
        return Err(RouteError::InvalidRouteConfig.into());
    }
    
    let mut weighted_sum = 0u64;
    let mut total_weight = 0u64;
    
    for i in 0..prices.len().saturating_sub(1) {
        let (price, timestamp) = prices[i];
        let next_timestamp = prices[i + 1].1;
        
        // 计算时间权重
        let weight = next_timestamp.saturating_sub(timestamp)
            .min(end_time)
            .saturating_sub(start_time.max(timestamp));
        
        if weight > 0 {
            weighted_sum = safe_add(weighted_sum, safe_mul(price, weight)?)?;
            total_weight = safe_add(total_weight, weight)?;
        }
    }
    
    if total_weight == 0 {
        return Err(RouteError::PriceCalculationError.into());
    }
    
    safe_div(weighted_sum, total_weight)
}

/// 计算指数移动平均（EMA）
pub fn calculate_exponential_moving_average(
    current_price: u64,
    previous_ema: u64,
    alpha_bps: u16, // 平滑系数（基点）
) -> Result<u64> {
    // EMA = alpha * current_price + (1 - alpha) * previous_ema
    let alpha_weight = safe_mul(current_price, alpha_bps as u64)?;
    let previous_weight = safe_mul(
        previous_ema, 
        safe_sub(10000, alpha_bps as u64)?
    )?;
    
    let total = safe_add(alpha_weight, previous_weight)?;
    safe_div(total, 10000)
}

/// 整数平方根计算（使用牛顿迭代法）
pub fn integer_sqrt(n: u64) -> Result<u64> {
    if n == 0 {
        return Ok(0);
    }
    
    let mut x = n;
    let mut y = (x + 1) / 2;
    
    while y < x {
        x = y;
        y = (x + n / x) / 2;
    }
    
    Ok(x)
}

/// 计算复合年化收益率（APY）
pub fn calculate_apy(
    principal: u64,
    final_amount: u64,
    time_period_seconds: u64,
) -> Result<u16> {
    if principal == 0 || time_period_seconds == 0 {
        return Ok(0);
    }
    
    if final_amount <= principal {
        return Ok(0); // 没有收益
    }
    
    let profit = safe_sub(final_amount, principal)?;
    let profit_rate = safe_div(safe_mul(profit, 10000)?, principal)?;
    
    // 年化处理（简化计算）
    const SECONDS_PER_YEAR: u64 = 365 * 24 * 3600;
    let annualized_rate = safe_div(
        safe_mul(profit_rate, SECONDS_PER_YEAR)?,
        time_period_seconds
    )?;
    
    Ok((annualized_rate as u16).min(u16::MAX))
}

/// 计算最大抽取（风险指标）
pub fn calculate_max_drawdown(
    prices: &[u64],
) -> Result<u16> {
    if prices.len() < 2 {
        return Ok(0);
    }
    
    let mut max_price = prices[0];
    let mut max_drawdown = 0u64;
    
    for &price in prices.iter().skip(1) {
        if price > max_price {
            max_price = price;
        } else {
            let drawdown = safe_div(
                safe_mul(safe_sub(max_price, price)?, 10000)?,
                max_price
            )?;
            max_drawdown = max_drawdown.max(drawdown);
        }
    }
    
    Ok((max_drawdown as u16).min(10000))
}

/// 计算复合增长率
pub fn calculate_compound_growth(
    principal: u64,
    rate_bps: u16,
    periods: u32,
) -> Result<u64> {
    if periods == 0 {
        return Ok(principal);
    }
    
    let mut result = principal;
    let multiplier = safe_add(10000, rate_bps as u64)?;
    
    for _ in 0..periods {
        result = safe_div(safe_mul(result, multiplier)?, 10000)?;
    }
    
    Ok(result)
}

/// 计算连续复利收益（使用线性近似）
pub fn calculate_continuous_compound_return(
    principal: u64,
    annual_rate_bps: u16,
    time_fraction: u64, // scaled by 10000 (e.g. 5000 = 0.5 years)
) -> Result<u64> {
    // 使用 e^(r*t) 的线性近似: 1 + r*t + (r*t)^2/2
    let rate = annual_rate_bps as u64;
    let rt = safe_div(safe_mul(rate, time_fraction)?, 100_000_000)?; // rate * time / 10000 / 10000
    
    // 第一项: 1.0
    let term1 = 10000u64;
    
    // 第二项: r*t
    let term2 = rt;
    
    // 第三项: (r*t)^2/2
    let term3 = safe_div(safe_mul(rt, rt)?, 2)?;
    
    let multiplier = safe_add(safe_add(term1, term2)?, term3)?;
    
    safe_div(safe_mul(principal, multiplier)?, 10000)
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_safe_math_operations() {
        assert_eq!(safe_mul(100, 200).unwrap(), 20000);
        assert_eq!(safe_div(1000, 10).unwrap(), 100);
        assert_eq!(safe_add(100, 200).unwrap(), 300);
        assert_eq!(safe_sub(200, 100).unwrap(), 100);
        
        // 测试溢出
        assert!(safe_mul(u64::MAX, 2).is_err());
        assert!(safe_sub(100, 200).is_err());
        assert!(safe_div(100, 0).is_err());
    }
    
    #[test]
    fn test_calculate_bps() {
        assert_eq!(calculate_bps(10000, 300).unwrap(), 300); // 3%
        assert_eq!(calculate_bps(10000, 100).unwrap(), 100); // 1%
    }
    
    #[test]
    fn test_calculate_slippage() {
        assert_eq!(calculate_slippage_bps(1000, 950).unwrap(), 500); // 5%滑点
        assert_eq!(calculate_slippage_bps(1000, 1050).unwrap(), 0);  // 正滑点
    }
    
    #[test]
    fn test_integer_sqrt() {
        assert_eq!(integer_sqrt(0).unwrap(), 0);
        assert_eq!(integer_sqrt(1).unwrap(), 1);
        assert_eq!(integer_sqrt(4).unwrap(), 2);
        assert_eq!(integer_sqrt(9).unwrap(), 3);
        assert_eq!(integer_sqrt(15).unwrap(), 3); // 向下取整
        assert_eq!(integer_sqrt(16).unwrap(), 4);
    }
    
    #[test]
    fn test_compound_growth() {
        // 10000 主金，10%年化收益，2年
        let result = calculate_compound_growth(10000, 1000, 2).unwrap();
        assert_eq!(result, 12100); // 10000 * 1.1^2
    }
    
    #[test]
    fn test_exponential_moving_average() {
        // 当前价格 1100，前一个EMA 1000，平滑系数 20%
        let ema = calculate_exponential_moving_average(1100, 1000, 2000).unwrap();
        assert_eq!(ema, 1020); // 0.2 * 1100 + 0.8 * 1000
    }
    
    #[test]
    fn test_max_drawdown() {
        let prices = vec![1000, 1100, 900, 800, 1200];
        let drawdown = calculate_max_drawdown(&prices).unwrap();
        // 最大回撤是从1100到800，约27.27%
        assert!(drawdown >= 2700 && drawdown <= 2730);
    }
    
    #[test]
    fn test_calculate_apy() {
        // 1000主金，1100最终金额，365天
        let apy = calculate_apy(1000, 1100, 365 * 24 * 3600).unwrap();
        assert_eq!(apy, 1000); // 10% APY
    }
    
    #[test]
    fn test_continuous_compound_return() {
        // 1000主金，10%年化收益，0.5年
        let result = calculate_continuous_compound_return(1000, 1000, 5000).unwrap();
        // 应该约等于 1000 * e^(0.1 * 0.5) ≈ 1051
        assert!(result >= 1040 && result <= 1060);
    }
}
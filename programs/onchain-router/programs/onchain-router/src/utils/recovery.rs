//! 错误恢复机制和重试策略
//!
//! 提供智能错误恢复、重试策略和降级处理能力

use anchor_lang::prelude::*;
use crate::{ErrorRecovery, ErrorRecoveryAction, ErrorRecoverySession, ErrorStatistics, RouteError};
use crate::routing::{Dex, RouteStep};
use crate::utils::logging::{log_recovery_attempt, LogContext, OperationType, log_arbitrage_operation, LogLevel};
use crate::state::event::{ErrorRecoveryEvent, SecurityAlert, SecurityAlertType, SecuritySeverity};

/// 重试策略
#[derive(Debug, Clone)]
pub struct RetryStrategy {
    pub max_attempts: u8,
    pub base_delay_ms: u64,
    pub backoff_multiplier: f64,
    pub max_delay_ms: u64,
    pub jitter_factor: f64,
}

impl Default for RetryStrategy {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            base_delay_ms: 100,
            backoff_multiplier: 2.0,
            max_delay_ms: 5000,
            jitter_factor: 0.1,
        }
    }
}

impl RetryStrategy {
    pub fn for_error(error: &RouteError) -> Self {
        match error {
            RouteError::InsufficientLiquidity => Self {
                max_attempts: 3,
                base_delay_ms: 2000,
                backoff_multiplier: 1.5,
                max_delay_ms: 10000,
                jitter_factor: 0.2,
            },
            RouteError::SlippageTooHigh => Self {
                max_attempts: 5,
                base_delay_ms: 1000,
                backoff_multiplier: 1.2,
                max_delay_ms: 3000,
                jitter_factor: 0.1,
            },
            RouteError::DexCpiCallFailed => Self {
                max_attempts: 3,
                base_delay_ms: 300,
                backoff_multiplier: 2.0,
                max_delay_ms: 2000,
                jitter_factor: 0.15,
            },
            RouteError::OperationTimeout => Self {
                max_attempts: 2,
                base_delay_ms: 500,
                backoff_multiplier: 2.0,
                max_delay_ms: 1500,
                jitter_factor: 0.1,
            },
            _ => Self::default(),
        }
    }

    pub fn calculate_delay(&self, attempt: u8) -> u64 {
        if attempt == 0 {
            return 0;
        }

        let delay = (self.base_delay_ms as f64) * self.backoff_multiplier.powi((attempt - 1) as i32);
        let max_delay = self.max_delay_ms as f64;
        let base_delay = delay.min(max_delay);

        // 添加抖动以避免同时重试
        let jitter = base_delay * self.jitter_factor * (attempt as f64 % 1.0);
        (base_delay + jitter) as u64
    }
}

/// 路由恢复管理器（简化版本）
#[derive(Debug)]
pub struct RouteRecoveryManager {
    // 简化版本，不使用HashMap
    pub current_session: Option<(u64, ErrorRecoverySession)>, // (order_id, session)
    pub statistics: ErrorStatistics,
    pub global_strategy: RetryStrategy,
}

impl RouteRecoveryManager {
    pub fn new() -> Self {
        Self {
            current_session: None,
            statistics: ErrorStatistics::new(),
            global_strategy: RetryStrategy::default(),
        }
    }

    /// 开始错误恢复会话
    pub fn start_recovery(
        &mut self,
        order_id: u64,
        error: RouteError,
        user: Pubkey,
    ) -> Result<ErrorRecoveryAction> {
        // 记录错误统计
        self.statistics.record_error(&error);

        // 检查是否为致命错误
        if !error.can_retry() {
            log_recovery_attempt(&error, &ErrorRecoveryAction::AbortRoute, 0, Some(&user), Some(order_id));
            return Err(Error::from(error));
        }

        // 创建或获取恢复会话（简化版本）
        let session = if let Some((current_order_id, ref mut current_session)) = &mut self.current_session {
            if *current_order_id == order_id {
                current_session
            } else {
                // 如果是不同的订单，创建新会话
                self.current_session = Some((order_id, ErrorRecoverySession::new(error.clone())));
                &mut self.current_session.as_mut().unwrap().1
            }
        } else {
            // 创建新会话
            self.current_session = Some((order_id, ErrorRecoverySession::new(error.clone())));
            &mut self.current_session.as_mut().unwrap().1
        };

        if !session.can_retry() {
            log_recovery_attempt(&error, &ErrorRecoveryAction::AbortRoute, session.attempt_count, Some(&user), Some(order_id));
            return Err(Error::from(RouteError::OperationTimeout));
        }

        // 获取恢复动作
        if let Some(action) = session.get_next_action() {
            session.record_attempt(action.clone());
            log_recovery_attempt(&error, &action, session.attempt_count, Some(&user), Some(order_id));

            // 发射错误恢复事件
            emit!(ErrorRecoveryEvent {
                user,
                order_id,
                original_error_code: error as u32,
                recovery_action: format!("{:?}", action),
                attempt_number: session.attempt_count,
                success: false,
                execution_time_ms: 0,
                additional_context: "Recovery attempt started".to_string(),
                timestamp: Clock::get().map(|c| c.unix_timestamp).unwrap_or(0),
            });

            Ok(action)
        } else {
            Err(Error::from(RouteError::OperationTimeout))
        }
    }

    /// 标记恢复成功
    pub fn mark_recovery_success(&mut self, order_id: u64, user: Pubkey) {
        if let Some((current_order_id, ref mut session)) = &mut self.current_session {
            if *current_order_id == order_id {
                session.mark_success();
                self.statistics.record_recovery(true);

                // 发射成功恢复事件
                emit!(ErrorRecoveryEvent {
                    user,
                    order_id,
                    original_error_code: session.error as u32,
                    recovery_action: "Success".to_string(),
                    attempt_number: session.attempt_count,
                    success: true,
                    execution_time_ms: 0,
                    additional_context: "Recovery completed successfully".to_string(),
                    timestamp: Clock::get().map(|c| c.unix_timestamp).unwrap_or(0),
                });

                log_arbitrage_operation(
                    LogLevel::Info,
                    "Error recovery successful",
                    &LogContext::new(OperationType::RouteExecution)
                        .with_user(user)
                        .with_order_id(order_id)
                        .with_data(format!("Attempts: {}", session.attempt_count)),
                );
            }
        }
    }

    /// 标记恢复失败
    pub fn mark_recovery_failure(&mut self, order_id: u64, user: Pubkey) {
        if let Some((current_order_id, ref session)) = &self.current_session {
            if *current_order_id == order_id {
                self.statistics.record_recovery(false);

                // 发射失败恢复事件
                emit!(ErrorRecoveryEvent {
                    user,
                    order_id,
                    original_error_code: session.error as u32,
                    recovery_action: "Failed".to_string(),
                    attempt_number: session.attempt_count,
                    success: false,
                    execution_time_ms: 0,
                    additional_context: "Recovery failed after all attempts".to_string(),
                    timestamp: Clock::get().map(|c| c.unix_timestamp).unwrap_or(0),
                });

                log_arbitrage_operation(
                    LogLevel::Error,
                    "Error recovery failed",
                    &LogContext::new(OperationType::RouteExecution)
                        .with_user(user)
                        .with_order_id(order_id)
                        .with_data(format!("Max attempts reached: {}", session.attempt_count)),
                );
            }
        }
    }

    /// 清理完成的会话
    pub fn cleanup_sessions(&mut self, max_age_seconds: i64) {
        let current_time = Clock::get().map(|c| c.unix_timestamp).unwrap_or(0);
        if let Some((_, ref session)) = &self.current_session {
            if current_time - session.last_attempt_time >= max_age_seconds {
                self.current_session = None;
            }
        }
    }

    /// 获取系统健康状态
    pub fn get_health_score(&self) -> u8 {
        self.statistics.get_error_health_score()
    }

    /// 检查是否需要触发安全警报
    pub fn check_security_alerts(&self, user: Option<Pubkey>) {
        if self.statistics.is_error_burst_detected() {
            SecurityAlert::emit_alert(
                SecurityAlertType::UnusualVolume,
                SecuritySeverity::Medium,
                user,
                "Error burst detected".to_string(),
                format!("Error count: {} in burst window", self.statistics.error_burst_count),
            );
        }

        if self.statistics.fatal_error_rate() > 0.5 {
            SecurityAlert::emit_alert(
                SecurityAlertType::SuspiciousPattern,
                SecuritySeverity::High,
                user,
                "High fatal error rate".to_string(),
                format!("Fatal error rate: {:.2}%", self.statistics.fatal_error_rate() * 100.0),
            );
        }
    }
}

/// 路由降级策略
#[derive(Debug, Clone)]
pub struct RouteDegradationStrategy {
    pub fallback_dexes: Vec<Dex>,
    pub amount_reduction_steps: Vec<u8>, // 减少百分比步骤
    pub max_slippage_increase: u16,      // 最大滑点增加（基点）
    pub enable_partial_execution: bool,
}

impl Default for RouteDegradationStrategy {
    fn default() -> Self {
        Self {
            fallback_dexes: vec![Dex::RaydiumClmm, Dex::MeteoraLb, Dex::Orca],
            amount_reduction_steps: vec![10, 25, 50],
            max_slippage_increase: 200, // 2%
            enable_partial_execution: true,
        }
    }
}

/// 路由修复器
pub struct RouteRepairer {
    degradation_strategy: RouteDegradationStrategy,
}

impl RouteRepairer {
    pub fn new(strategy: RouteDegradationStrategy) -> Self {
        Self {
            degradation_strategy: strategy,
        }
    }

    /// 尝试修复路由
    pub fn attempt_route_repair(
        &self,
        original_route: &[RouteStep],
        failed_step: u8,
        error: &RouteError,
        user: Pubkey,
        order_id: u64,
    ) -> Result<Vec<RouteStep>> {
        let failed_step_index = failed_step as usize;

        if failed_step_index >= original_route.len() {
            return Err(Error::from(RouteError::InvalidRouteConfig));
        }

        let mut repaired_route = original_route.to_vec();

        match error {
            RouteError::InsufficientLiquidity | RouteError::DexAdapterFailed => {
                // 尝试切换DEX
                if let Some(alternative_dex) = self.find_alternative_dex(
                    &original_route[failed_step_index].dex,
                ) {
                    repaired_route[failed_step_index].dex = alternative_dex;
                    log_arbitrage_operation(
                        LogLevel::Info,
                        "Route repaired with alternative DEX",
                        &LogContext::new(OperationType::RouteExecution)
                            .with_user(user)
                            .with_order_id(order_id)
                            .with_step(failed_step)
                            .with_dex(alternative_dex),
                    );
                    return Ok(repaired_route);
                }
            },
            RouteError::SlippageTooHigh | RouteError::PriceImpactTooHigh => {
                // 尝试减少交易金额
                for &reduction_pct in &self.degradation_strategy.amount_reduction_steps {
                    let new_amount = repaired_route[failed_step_index].amount_in * (100 - reduction_pct as u64) / 100;
                    if new_amount > 0 {
                        repaired_route[failed_step_index].amount_in = new_amount;
                        // 更新后续步骤的金额
                        self.update_subsequent_amounts(&mut repaired_route, failed_step_index);

                        log_arbitrage_operation(
                            LogLevel::Info,
                            "Route repaired with reduced amount",
                            &LogContext::new(OperationType::RouteExecution)
                                .with_user(user)
                                .with_order_id(order_id)
                                .with_step(failed_step)
                                .with_amount(new_amount)
                                .with_data(format!("Reduction: {}%", reduction_pct)),
                        );
                        return Ok(repaired_route);
                    }
                }
            },
            RouteError::RoutePathTooLong => {
                // 尝试简化路由
                if let Some(simplified_route) = self.simplify_route(&repaired_route) {
                    log_arbitrage_operation(
                        LogLevel::Info,
                        "Route repaired with simplified path",
                        &LogContext::new(OperationType::RouteExecution)
                            .with_user(user)
                            .with_order_id(order_id)
                            .with_data(format!("Steps reduced from {} to {}", original_route.len(), simplified_route.len())),
                    );
                    return Ok(simplified_route);
                }
            },
            _ => {
                // 对于其他错误，尝试通用修复策略
                if let Some(alternative_dex) = self.find_alternative_dex(
                    &original_route[failed_step_index].dex,
                ) {
                    repaired_route[failed_step_index].dex = alternative_dex;
                    return Ok(repaired_route);
                }
            }
        }

        Err(Error::from(RouteError::UnknownError))
    }

    /// 查找替代DEX
    fn find_alternative_dex(&self, failed_dex: &Dex) -> Option<Dex> {
        self.degradation_strategy
            .fallback_dexes
            .iter()
            .find(|&dex| dex != failed_dex)
            .copied()
    }

    /// 更新后续步骤的金额
    fn update_subsequent_amounts(&self, route: &mut [RouteStep], from_step: usize) {
        // 简化实现：假设后续步骤的金额按比例调整
        // 在实际实现中，这需要根据具体的交换曲线重新计算
        for i in (from_step + 1)..route.len() {
            route[i].amount_in = route[i - 1].expected_amount_out;
        }
    }

    /// 简化路由
    fn simplify_route(&self, route: &[RouteStep]) -> Option<Vec<RouteStep>> {
        if route.len() <= 2 {
            return None;
        }

        // 尝试移除中间步骤，直接连接起始和结束
        if route.len() == 3 {
            let first = &route[0];
            let last = &route[2];

            // 检查是否可以直接从第一个token交换到最后一个token
            if first.token_out == last.token_in {
                return Some(vec![
                    RouteStep {
                        dex: first.dex,
                        token_in: first.token_in,
                        token_out: last.token_out,
                        amount_in: first.amount_in,
                        expected_amount_out: last.expected_amount_out,
                        pool_address: first.pool_address, // 可能需要重新计算
                    }
                ]);
            }
        }

        None
    }
}

/// 批量恢复操作
pub struct BatchRecoveryProcessor {
    recovery_manager: RouteRecoveryManager,
    #[allow(dead_code)]
    route_repairer: RouteRepairer,
}

impl BatchRecoveryProcessor {
    pub fn new() -> Self {
        Self {
            recovery_manager: RouteRecoveryManager::new(),
            route_repairer: RouteRepairer::new(RouteDegradationStrategy::default()),
        }
    }

    /// 批量处理恢复
    pub fn process_batch_recovery(
        &mut self,
        failed_routes: Vec<(u64, RouteError, Pubkey)>, // (order_id, error, user)
    ) -> Vec<(u64, Result<ErrorRecoveryAction>)> {
        let mut results = Vec::new();

        for (order_id, error, user) in failed_routes {
            let result = self.recovery_manager.start_recovery(order_id, error, user);
            results.push((order_id, result));
        }

        // 检查是否需要触发安全警报
        self.recovery_manager.check_security_alerts(None);

        results
    }

    /// 获取恢复统计信息
    pub fn get_recovery_statistics(&self) -> &ErrorStatistics {
        &self.recovery_manager.statistics
    }

    /// 清理过期会话
    pub fn cleanup(&mut self) {
        self.recovery_manager.cleanup_sessions(3600); // 1小时
    }
}

/// 智能重试装饰器
pub struct SmartRetryWrapper<F> {
    operation: F,
    strategy: RetryStrategy,
}

impl<F, T> SmartRetryWrapper<F>
where
    F: Fn() -> Result<T>,
{
    pub fn new(operation: F, strategy: RetryStrategy) -> Self {
        Self { operation, strategy }
    }

    pub fn execute(&self) -> Result<T> {
        let mut last_error: Option<Error> = None;

        for attempt in 0..self.strategy.max_attempts {
            match (self.operation)() {
                Ok(result) => return Ok(result),
                Err(error) => {
                    // 尝试从错误中提取 RouteError 来检查重试策略
                    let route_error = RouteError::from(error);

                    if !route_error.can_retry() || attempt == self.strategy.max_attempts - 1 {
                        last_error = Some(Error::from(route_error));
                        break;
                    }

                    let delay = self.strategy.calculate_delay(attempt + 1);
                    if delay > 0 {
                        // 在实际实现中，这里需要使用适当的延迟机制
                        // 在Solana程序中，我们不能使用std::thread::sleep
                        // 可以通过记录延迟时间并在下次调用时检查来实现
                    }

                    last_error = Some(Error::from(route_error));
                }
            }
        }

        Err(last_error.unwrap_or_else(|| Error::from(RouteError::UnknownError)))
    }
}

/// 恢复策略构建器
pub struct RecoveryStrategyBuilder {
    strategy: RouteDegradationStrategy,
}

impl RecoveryStrategyBuilder {
    pub fn new() -> Self {
        Self {
            strategy: RouteDegradationStrategy::default(),
        }
    }

    pub fn with_fallback_dexes(mut self, dexes: Vec<Dex>) -> Self {
        self.strategy.fallback_dexes = dexes;
        self
    }

    pub fn with_amount_reductions(mut self, reductions: Vec<u8>) -> Self {
        self.strategy.amount_reduction_steps = reductions;
        self
    }

    pub fn with_max_slippage_increase(mut self, increase_bps: u16) -> Self {
        self.strategy.max_slippage_increase = increase_bps;
        self
    }

    pub fn enable_partial_execution(mut self, enable: bool) -> Self {
        self.strategy.enable_partial_execution = enable;
        self
    }

    pub fn build(self) -> RouteDegradationStrategy {
        self.strategy
    }
}

//! 闪电贷模块
//!
//! 提供多协议闪电贷集成，支持自动最优费率选择和风险管理

pub mod traits;
pub mod kamino;
pub mod manager;
pub mod callback;


// 重新导出主要类型和trait
pub use traits::{
    FlashLoanProvider,
    FlashLoanManager,
    FlashLoanCallback,
    FlashLoanContext,
    FlashLoanStats,
};

pub use kamino::{
    KaminoFlashLoan,
    KaminoCallbackData,
    KAMINO_PROGRAM_ID,
};

pub use manager::{
    FlashLoanManagerImpl,
    ProviderInfo,
    ProviderMonitor,
    ProviderAction,
};

pub use callback::{
    FlashLoanCallbackHandler,
    AdvancedCallbackHandler,
    CallbackData,
    FlashLoanCallbackExecutedEvent,
};

use anchor_lang::prelude::*;
use crate::error::RouteError;

/// 闪电贷服务的主要入口点
///
/// 提供统一的闪电贷接口，自动选择最优提供者和处理回调
pub struct FlashLoanService {
    /// 闪电贷管理器
    manager: FlashLoanManagerImpl,
    /// 回调处理器
    callback_handler: FlashLoanCallbackHandler,
    /// 是否启用性能监控
    monitoring_enabled: bool,
}

impl Default for FlashLoanService {
    fn default() -> Self {
        Self {
            manager: FlashLoanManagerImpl::new(100, true), // 最大1%费率，启用自动故障转移
            callback_handler: FlashLoanCallbackHandler::default(),
            monitoring_enabled: true,
        }
    }
}

impl FlashLoanService {
    /// 创建新的闪电贷服务
    pub fn new(max_fee_bps: u16, enable_monitoring: bool) -> Self {
        Self {
            manager: FlashLoanManagerImpl::new(max_fee_bps, true),
            callback_handler: FlashLoanCallbackHandler::new(6, 300, enable_monitoring),
            monitoring_enabled: enable_monitoring,
        }
    }

    pub(crate) fn get_best_provider(&self, _p0: u64, _p1: &Pubkey) -> Result<Box<dyn FlashLoanProvider>> {
        todo!()
    }

    /// 执行闪电贷套利
    ///
    /// # 参数
    /// - `accounts`: 相关账户信息
    /// - `amount`: 借贷数量
    /// - `mint`: 代币mint地址
    /// - `callback_data`: 回调数据（包含套利路由）
    ///
    /// # 返回
    /// 返回净利润金额
    pub fn execute_flash_loan_arbitrage<'info>(
        &mut self,
        accounts: &[AccountInfo<'info>],
        amount: u64,
        mint: &Pubkey,
        callback_data: &[u8],
    ) -> Result<u64> {
        msg!("开始执行闪电贷套利 - 金额: {}, 代币: {}", amount, mint);

        let start_time = Clock::get()?.unix_timestamp;

        // 1. 验证输入参数
        if amount == 0 {
            return Err(RouteError::InvalidFlashLoanAmount.into());
        }

        // 2. 验证回调数据
        self.callback_handler.validate_callback_data(callback_data)?;

        // 3. 选择最优提供者
        let provider = self.manager.get_best_provider(amount, mint)?;
        let provider_id = provider.get_program_id();

        if self.monitoring_enabled {
            msg!("选择闪电贷提供者: {} (程序ID: {})",
                provider.get_provider_name(), provider_id);
        }

        // 4. 发起闪电贷
        let context = provider.initiate_flash_loan(accounts, amount, mint, callback_data)?;

        // 5. 执行回调（套利逻辑）
        let result = self.callback_handler.execute_callback(&context, accounts, callback_data);

        let execution_time = Clock::get()?.unix_timestamp - start_time;

        // 6. 更新提供者统计
        match &result {
            Ok(profit) => {
                if self.monitoring_enabled {
                    self.manager.update_provider_stats(
                        &provider_id,
                        true,
                        amount,
                        context.fee,
                        *profit,
                        execution_time as u64 * 1000, // 转换为毫秒
                    )?;
                }
                msg!("闪电贷套利成功 - 利润: {}, 执行时间: {}s", profit, execution_time);
            }
            Err(_) => {
                if self.monitoring_enabled {
                    self.manager.update_provider_stats(
                        &provider_id,
                        false,
                        amount,
                        context.fee,
                        0,
                        execution_time as u64 * 1000,
                    )?;
                }
                msg!("闪电贷套利失败 - 执行时间: {}s", execution_time);
            }
        }

        result
    }

    /// 获取指定代币的最佳闪电贷条件
    pub fn get_best_flash_loan_terms(&self, amount: u64, mint: &Pubkey) -> Result<FlashLoanTerms> {
        let provider = self.manager.get_best_provider(amount, mint)?;

        let max_amount = provider.get_max_loan_amount(mint)?;
        let fee = provider.calculate_fee(amount)?;
        let fee_bps = provider.get_fee_bps();

        Ok(FlashLoanTerms {
            provider_name: provider.get_provider_name(),
            program_id: provider.get_program_id(),
            max_amount,
            fee,
            fee_bps,
            available_amount: max_amount.min(amount),
        })
    }

    /// 获取所有支持指定代币的提供者信息
    pub fn get_available_providers(&self, mint: &Pubkey) -> Vec<ProviderTerms> {
        let providers = self.manager.get_providers_for_mint(mint);
        let mut terms = Vec::new();

        for provider in providers {
            if let Ok(max_amount) = provider.get_max_loan_amount(mint) {
                terms.push(ProviderTerms {
                    name: provider.get_provider_name(),
                    program_id: provider.get_program_id(),
                    max_amount,
                    fee_bps: provider.get_fee_bps(),
                    supported: true,
                });
            }
        }

        terms
    }

    /// 预估闪电贷套利的潜在利润
    pub fn estimate_arbitrage_profit(
        &self,
        amount: u64,
        mint: &Pubkey,
        callback_data: &[u8],
    ) -> Result<ProfitEstimate> {
        // 1. 获取最佳提供者条件
        let terms = self.get_best_flash_loan_terms(amount, mint)?;

        // 2. 解析回调数据获取预期利润
        let callback = CallbackData::try_from_slice(callback_data)
            .map_err(|_| RouteError::InvalidCallbackData)?;

        // 3. 估算总成本
        let flash_loan_cost = terms.fee;
        let estimated_gas_cost = 50_000; // 基础估算，实际应该更精确
        let total_cost = flash_loan_cost + estimated_gas_cost;

        // 4. 计算净利润预估
        let estimated_net_profit = if callback.expected_profit > total_cost {
            callback.expected_profit - total_cost
        } else {
            0
        };

        Ok(ProfitEstimate {
            gross_profit: callback.expected_profit,
            flash_loan_fee: flash_loan_cost,
            estimated_gas_cost,
            net_profit: estimated_net_profit,
            profit_margin_bps: if callback.expected_profit > 0 {
                ((estimated_net_profit * 10000) / callback.expected_profit) as u16
            } else {
                0
            },
            success_probability: self.calculate_success_probability(&terms, &callback),
        })
    }

    /// 计算成功概率（基于历史数据和市场条件）
    fn calculate_success_probability(&self, terms: &FlashLoanTerms, callback: &CallbackData) -> f64 {
        // 基础概率
        let mut probability = 0.8;

        // 根据滑点调整
        if callback.max_slippage_bps > 500 {
            probability *= 0.9; // 高滑点降低成功率
        } else if callback.max_slippage_bps < 100 {
            probability *= 0.8; // 低滑点也可能失败
        }

        // 根据路由复杂度调整
        if callback.routes.len() > 4 {
            probability *= 0.85; // 复杂路由降低成功率
        }

        // 根据提供者历史表现调整
        if let Some(info) = self.manager.get_provider_info(&terms.program_id) {
            let provider_success_rate = info.stats.success_rate();
            if provider_success_rate > 0.0 {
                probability *= provider_success_rate;
            }
        }

        probability.min(1.0).max(0.0)
    }

    /// 启用/禁用性能监控
    pub fn set_monitoring_enabled(&mut self, enabled: bool) {
        self.monitoring_enabled = enabled;
    }

    /// 获取管理器引用（用于高级配置）
    pub fn get_manager(&self) -> &FlashLoanManagerImpl {
        &self.manager
    }

    /// 获取管理器可变引用（用于高级配置）
    pub fn get_manager_mut(&mut self) -> &mut FlashLoanManagerImpl {
        &mut self.manager
    }
}

/// 闪电贷条件信息
#[derive(Debug, Clone)]
pub struct FlashLoanTerms {
    /// 提供者名称
    pub provider_name: &'static str,
    /// 程序ID
    pub program_id: Pubkey,
    /// 最大可借数量
    pub max_amount: u64,
    /// 费用
    pub fee: u64,
    /// 费率（基点）
    pub fee_bps: u16,
    /// 可用数量
    pub available_amount: u64,
}

/// 提供者条件信息
#[derive(Debug, Clone)]
pub struct ProviderTerms {
    /// 提供者名称
    pub name: &'static str,
    /// 程序ID
    pub program_id: Pubkey,
    /// 最大可借数量
    pub max_amount: u64,
    /// 费率（基点）
    pub fee_bps: u16,
    /// 是否支持
    pub supported: bool,
}

/// 利润预估信息
#[derive(Debug, Clone)]
pub struct ProfitEstimate {
    /// 毛利润
    pub gross_profit: u64,
    /// 闪电贷费用
    pub flash_loan_fee: u64,
    /// 预估Gas成本
    pub estimated_gas_cost: u64,
    /// 净利润
    pub net_profit: u64,
    /// 利润率（基点）
    pub profit_margin_bps: u16,
    /// 成功概率（0-1）
    pub success_probability: f64,
}

/// 创建默认的闪电贷服务实例
pub fn create_flash_loan_service() -> FlashLoanService {
    FlashLoanService::default()
}

/// 创建配置的闪电贷服务实例
pub fn create_configured_flash_loan_service(
    max_fee_bps: u16,
    enable_monitoring: bool,
) -> FlashLoanService {
    FlashLoanService::new(max_fee_bps, enable_monitoring)
}

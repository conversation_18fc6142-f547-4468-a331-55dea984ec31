//! 常量定义模块
//!
//! 集成和重用dex-instructions中的程序ID和常量定义
//! 避免重复定义，确保一致性

use anchor_lang::prelude::*;

// 重新导出dex-instructions中的常量
pub mod dex_program_ids {
    use anchor_lang::prelude::*;

    /// Raydium CLMM程序ID
    pub const RAYDIUM_CLMM_PROGRAM_ID: Pubkey = anchor_lang::pubkey!("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK");

    /// Raydium CPMM程序ID
    pub const RAYDIUM_CPMM_PROGRAM_ID: Pubkey = anchor_lang::pubkey!("CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C");

    /// Meteora DAMM程序ID
    pub const METEORA_DAMM_PROGRAM_ID: Pubkey = anchor_lang::pubkey!("cpamdpZCGKUy5JxQXB4dcpGPiikHawvSWAd6mEn1sGG");

    /// Meteora DLMM程序ID
    pub const METEORA_DLMM_PROGRAM_ID: Pubkey = anchor_lang::pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo");

    /// PumpSwap程序ID
    pub const PUMP_PROGRAM_ID: Pubkey = anchor_lang::pubkey!("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");

    /// Orca Whirlpool程序ID (预留)
    pub const ORCA_WHIRLPOOL_PROGRAM_ID: Pubkey = anchor_lang::pubkey!("whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc");
}

// 通用常量
pub mod common {
    use anchor_lang::prelude::*;

    /// SPL Token程序ID
    pub const TOKEN_PROGRAM_ID: Pubkey = anchor_lang::pubkey!("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");

    /// SPL Token 2022程序ID
    pub const TOKEN_PROGRAM_2022_ID: Pubkey = anchor_lang::pubkey!("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb");

    /// SPL关联代币账户程序ID
    pub const ASSOCIATED_TOKEN_PROGRAM_ID: Pubkey = anchor_lang::pubkey!("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL");

    /// 系统程序ID
    pub const SYSTEM_PROGRAM_ID: Pubkey = anchor_lang::pubkey!("11111111111111111111111111111111");

    /// Memo程序ID
    pub const MEMO_PROGRAM_ID: Pubkey = anchor_lang::pubkey!("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr");

    /// WSOL代币Mint地址
    pub const WSOL_TOKEN_MINT: Pubkey = anchor_lang::pubkey!("So11111111111111111111111111111111111111112");
}

// 路由配置常量
pub mod routing {
    /// 最大路由跳数
    pub const MAX_ROUTE_HOPS: usize = 6;

    /// 最大滑点（基点）
    pub const MAX_SLIPPAGE_BPS: u16 = 1000; // 10%

    /// 默认滑点（基点）
    pub const DEFAULT_SLIPPAGE_BPS: u16 = 100; // 1%

    /// 最小交换金额（lamports）
    pub const MIN_SWAP_AMOUNT: u64 = 1000;

    /// 最大交换金额（lamports）
    pub const MAX_SWAP_AMOUNT: u64 = u64::MAX / 2;

    /// 基点最大值（100%）
    pub const BASIS_POINTS_MAX: u64 = 10000;
}

// 闪电贷配置常量
pub mod flash_loan {
    /// 最大闪电贷金额（lamports）
    pub const MAX_FLASH_LOAN_AMOUNT: u64 = 1_000_000 * 1_000_000_000; // 1B SOL

    /// 最大闪电贷费率（基点）
    pub const MAX_FLASH_LOAN_FEE_BPS: u16 = 100; // 1%

    /// 默认闪电贷费率（基点）
    pub const DEFAULT_FLASH_LOAN_FEE_BPS: u16 = 5; // 0.05%
}

// 安全控制常量
pub mod security {
    /// 重入攻击检测标记值
    pub const REENTRANCY_GUARD_VALUE: u8 = 1;

    /// 最大失败重试次数
    pub const MAX_RETRY_COUNT: u8 = 3;

    /// 操作超时时间（秒）
    pub const OPERATION_TIMEOUT_SECONDS: i64 = 30;

    /// 最大并发交换数量
    pub const MAX_CONCURRENT_SWAPS: u8 = 10;
}

/// 根据DEX类型获取程序ID
pub fn get_dex_program_id(dex: &crate::routing::types::Dex) -> Pubkey {
    use crate::routing::types::Dex;
    use dex_program_ids::*;

    match dex {
        Dex::RaydiumClmm => RAYDIUM_CLMM_PROGRAM_ID,
        Dex::RaydiumCpmm => RAYDIUM_CPMM_PROGRAM_ID,
        Dex::MeteoraAmm => METEORA_DAMM_PROGRAM_ID,
        Dex::MeteoraLb => METEORA_DLMM_PROGRAM_ID,
        Dex::PumpSwap => PUMP_PROGRAM_ID,
        Dex::Orca => ORCA_WHIRLPOOL_PROGRAM_ID,
    }
}

/// 验证程序ID是否正确
pub fn validate_dex_program_id(dex: &crate::routing::types::Dex, provided_program_id: &Pubkey) -> bool {
    get_dex_program_id(dex) == *provided_program_id
}

/// 获取DEX的预期账户数量
pub fn get_expected_account_count(dex: &crate::routing::types::Dex) -> usize {
    use crate::routing::types::Dex;

    match dex {
        Dex::RaydiumClmm => 14, // CLMM需要更多账户
        Dex::RaydiumCpmm => 13, // CPMM账户数量
        Dex::MeteoraAmm => 14,  // DAMM账户数量
        Dex::MeteoraLb => 12,   // DLMM账户数量
        Dex::PumpSwap => 19,    // PumpSwap账户数量
        Dex::Orca => 16,        // Orca Whirlpool账户数量
    }
}

/// 检查是否为有效的代币mint
pub fn is_valid_token_mint(mint: &Pubkey) -> bool {
    // 基本验证：不能是系统程序等
    !mint.eq(&common::SYSTEM_PROGRAM_ID)
        && !mint.eq(&common::TOKEN_PROGRAM_ID)
        && !mint.eq(&common::TOKEN_PROGRAM_2022_ID)
        && !mint.eq(&Pubkey::default())
}

/// 计算基点百分比
pub fn calculate_percentage_from_bps(bps: u16) -> f64 {
    (bps as f64) / (routing::BASIS_POINTS_MAX as f64) * 100.0
}

/// 从百分比计算基点
pub fn calculate_bps_from_percentage(percentage: f64) -> u16 {
    ((percentage / 100.0) * (routing::BASIS_POINTS_MAX as f64)) as u16
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::routing::Dex;

    #[test]
    fn test_get_dex_program_id() {
        let raydium_clmm_id = get_dex_program_id(&Dex::RaydiumClmm);
        assert_eq!(raydium_clmm_id, dex_program_ids::RAYDIUM_CLMM_PROGRAM_ID);

        let pump_id = get_dex_program_id(&Dex::PumpSwap);
        assert_eq!(pump_id, dex_program_ids::PUMP_PROGRAM_ID);
    }

    #[test]
    fn test_validate_dex_program_id() {
        let correct_id = dex_program_ids::RAYDIUM_CLMM_PROGRAM_ID;
        let incorrect_id = dex_program_ids::PUMP_PROGRAM_ID;

        assert!(validate_dex_program_id(&Dex::RaydiumClmm, &correct_id));
        assert!(!validate_dex_program_id(&Dex::RaydiumClmm, &incorrect_id));
    }

    #[test]
    fn test_is_valid_token_mint() {
        let valid_mint = Pubkey::new_unique();
        let invalid_mint = common::SYSTEM_PROGRAM_ID;

        assert!(is_valid_token_mint(&valid_mint));
        assert!(!is_valid_token_mint(&invalid_mint));
        assert!(!is_valid_token_mint(&Pubkey::default()));
    }

    #[test]
    fn test_bps_calculations() {
        assert_eq!(calculate_percentage_from_bps(100), 1.0); // 1%
        assert_eq!(calculate_percentage_from_bps(1000), 10.0); // 10%

        assert_eq!(calculate_bps_from_percentage(1.0), 100);
        assert_eq!(calculate_bps_from_percentage(10.0), 1000);
    }
}

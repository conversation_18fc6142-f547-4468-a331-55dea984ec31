use anchor_lang::prelude::*;
use anchor_lang::{AnchorSerialize, AnchorDeserialize};
use crate::error::RouteError;

/// 支持的DEX协议枚举
/// 基于现有的dex-instructions crate，支持主流DEX
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, AnchorSerialize, AnchorDeserialize)]
pub enum Dex {
    /// Raydium CLMM
    RaydiumClmm,
    /// Raydium CPMM
    RaydiumCpmm,
    /// Meteora DLMM
    MeteoraLb,
    /// Meteora DAMM
    MeteoraAmm,
    /// Orca Whirlpool
    Orca,
    /// Pump.fun
    PumpSwap,
}

/// 路由模式枚举
/// 定义不同的路由执行模式
#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub enum RoutingMode {
    /// 线性路由: A -> B -> C (普通多跳交换)
    Linear,
    /// 循环路由: A -> B -> C -> A (套利模式)
    Circular,
    /// 分支路由: A -> [B, C] -> D (分散聚合)
    Branching,
    /// 批量路由: [A1, A2] -> [B1, B2] (批量处理)
    Batched,
}

/// 单个路由步骤定义
/// 表示从一个代币到另一个代币的单次交换
#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct Route {
    /// 使用的DEX协议
    pub dex: Dex,
    /// 输入代币mint地址
    pub input_mint: Pubkey,
    /// 输出代币mint地址
    pub output_mint: Pubkey,
    /// DEX特定的交换数据（池子地址、费率等）
    pub swap_data: Vec<u8>,
    /// 预期的最小输出数量（滑点保护）
    pub min_amount_out: u64,
}

/// 路由执行步骤
/// 用于错误恢复和路由修复，包含运行时信息
#[derive(Debug, Clone)]
pub struct RouteStep {
    /// 使用的DEX协议
    pub dex: Dex,
    /// 输入代币mint地址
    pub token_in: Pubkey,
    /// 输出代币mint地址
    pub token_out: Pubkey,
    /// 实际输入数量
    pub amount_in: u64,
    /// 预期输出数量
    pub expected_amount_out: u64,
    /// 池子地址
    pub pool_address: Pubkey,
}

/// 完整的路由配置
/// 包含路由模式、路径和执行参数
#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct RouteConfig {
    /// 路由执行模式
    pub mode: RoutingMode,
    /// 路由路径（按执行顺序）
    pub routes: Vec<Route>,
    /// 输入数量
    pub amount_in: u64,
    /// 最终最小输出数量
    pub min_amount_out: u64,
    /// 闪电贷配置（可选）
    pub flash_loan: Option<FlashLoanConfig>,
    /// 最大滑点（基点，如100表示1%）
    pub max_slippage_bps: u16,
}

/// 闪电贷配置
/// 用于零本金套利操作
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct FlashLoanConfig {
    /// 闪电贷提供者程序地址
    pub provider: Pubkey,
    /// 闪电贷数量
    pub amount: u64,
    /// 最大可接受的费率（基点）
    pub max_fee_bps: u16,
    /// 抵押品代币mint（如果需要）
    pub collateral_mint: Option<Pubkey>,
}

/// 闪电贷路由配置
/// 专门用于闪电贷场景的配置
#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct FlashLoanRouteConfig {
    /// 闪电贷配置
    pub flash_loan: FlashLoanConfig,
    /// 套利路由配置
    pub arbitrage_routes: Vec<Route>,
    /// 预期利润（用于验证套利可行性）
    pub expected_profit: u64,
    /// 最大可接受的Gas费用
    pub max_gas_fee: u64,
}

/// 分支路由配置
/// 用于分散-聚合类型的路由
#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct BranchRouteConfig {
    /// 输入分配比例（和为10000，表示100%）
    pub input_distribution: Vec<u16>,
    /// 分支路由列表
    pub branch_routes: Vec<Vec<Route>>,
    /// 聚合目标代币
    pub target_mint: Pubkey,
}

/// 批量路由配置
/// 用于同时处理多个独立路由
#[derive(Debug, Clone, AnchorSerialize, AnchorDeserialize)]
pub struct BatchRouteConfig {
    /// 批量路由列表
    pub routes: Vec<RouteConfig>,
    /// 是否要求全部成功（原子性）
    pub atomic: bool,
}

/// 路由执行结果
#[derive(Debug)]
#[derive(Clone)]
pub struct RouteResult {
    /// 实际输入数量
    pub amount_in: u64,
    /// 实际输出数量
    pub amount_out: u64,
    /// 预期输出数量（用于滑点计算）
    pub expected_amount: Option<u64>,
    /// 执行的路由步骤数
    pub steps_executed: u8,
    /// 总Gas消耗
    pub gas_used: u64,
    /// 实际滑点（基点）
    pub actual_slippage_bps: u16,
    /// 净利润（仅适用于套利）
    pub net_profit: u64,
}

/// 分支路由执行结果
#[derive(Debug)]
pub struct BranchRouteResult {
    /// 总输出数量
    pub total_output: u64,
    /// 各分支的执行结果
    pub branch_results: Vec<RouteResult>,
    /// 各分支的输入数量
    pub branch_amounts: Vec<u64>,
    /// 总Gas消耗
    pub total_gas_used: u64,
    /// 平均滑点（基点）
    pub average_slippage_bps: u16,
}

/// 批量路由执行结果
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct BatchRouteResult {
    /// 各路由的输出数量列表
    pub route_outputs: Vec<u64>,
    /// 总输出数量
    pub total_output: u64,
    /// 成功执行的路由数量
    pub success_count: u8,
    /// 总路由数量
    pub total_routes: u8,
    /// 是否原子性执行
    pub atomic_execution: bool,
    /// 总Gas消耗
    pub total_gas_used: u64,
}

impl Route {
    /// 验证路由步骤的有效性
    pub fn validate(&self) -> Result<()> {
        // 检查输入和输出mint不能相同（除非是特殊情况）
        if self.input_mint == self.output_mint {
            return Err(RouteError::InvalidRouteConfig.into());
        }

        // 检查swap_data不为空
        if self.swap_data.is_empty() {
            return Err(RouteError::InvalidRouteConfig.into());
        }

        Ok(())
    }

    /// 获取路由步骤的显示名称
    pub fn display_name(&self) -> String {
        format!("{:?} Route", self.dex)
    }
}

impl RouteStep {
    /// 创建新的路由步骤
    pub fn new(
        dex: Dex,
        token_in: Pubkey,
        token_out: Pubkey,
        amount_in: u64,
        expected_amount_out: u64,
        pool_address: Pubkey,
    ) -> Self {
        Self {
            dex,
            token_in,
            token_out,
            amount_in,
            expected_amount_out,
            pool_address,
        }
    }

    /// 验证路由步骤的有效性
    pub fn validate(&self) -> Result<()> {
        // 检查输入和输出token不能相同
        if self.token_in == self.token_out {
            return Err(RouteError::InvalidRouteConfig.into());
        }

        // 检查金额有效性
        if self.amount_in == 0 || self.expected_amount_out == 0 {
            return Err(RouteError::AmountValidationFailed.into());
        }

        Ok(())
    }

    /// 从Route转换为RouteStep
    pub fn from_route(route: &Route, amount_in: u64, expected_amount_out: u64, pool_address: Pubkey) -> Self {
        Self {
            dex: route.dex.clone(),
            token_in: route.input_mint,
            token_out: route.output_mint,
            amount_in,
            expected_amount_out,
            pool_address,
        }
    }

    /// 计算预期滑点（基点）
    pub fn calculate_expected_slippage(&self, actual_amount_out: u64) -> u16 {
        if self.expected_amount_out == 0 {
            return 0;
        }

        let slippage = if actual_amount_out < self.expected_amount_out {
            ((self.expected_amount_out - actual_amount_out) * 10000) / self.expected_amount_out
        } else {
            0
        };

        slippage.min(10000) as u16 // 最大100%滑点
    }

    /// 获取路由步骤的显示名称
    pub fn display_name(&self) -> String {
        format!("{:?} Step", self.dex)
    }
}

impl Default for RouteConfig {
    fn default() -> Self {
        Self {
            mode: RoutingMode::Linear,
            routes: vec![],
            amount_in: 0,
            min_amount_out: 0,
            flash_loan: None,
            max_slippage_bps: 100, // 1%
        }
    }
}

impl RouteConfig {
    /// 验证完整路由配置的有效性
    pub fn validate(&self) -> Result<()> {
        // 检查路由不为空
        if self.routes.is_empty() {
            return Err(RouteError::EmptyRoutePath.into());
        }

        // 检查路由长度限制（最多6步）
        if self.routes.len() > 6 {
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 验证每个路由步骤
        for route in &self.routes {
            route.validate()?;
        }

        // 验证路由连续性
        self.validate_continuity()?;

        // 根据模式进行特定验证
        match self.mode {
            RoutingMode::Circular => self.validate_circular()?,
            RoutingMode::Linear => self.validate_linear()?,
            _ => {} // 其他模式暂时不需要特殊验证
        }

        Ok(())
    }

    /// 验证路由连续性（相邻步骤的代币要匹配）
    fn validate_continuity(&self) -> Result<()> {
        for i in 0..self.routes.len().saturating_sub(1) {
            if self.routes[i].output_mint != self.routes[i + 1].input_mint {
                return Err(RouteError::RouteDiscontinuity.into());
            }
        }
        Ok(())
    }

    /// 验证循环路由（套利模式）
    fn validate_circular(&self) -> Result<()> {
        if self.routes.len() < 2 {
            return Err(RouteError::InvalidRouteConfig.into());
        }

        let start_mint = self.routes.first().unwrap().input_mint;
        let end_mint = self.routes.last().unwrap().output_mint;

        if start_mint != end_mint {
            return Err(RouteError::NotCircularRoute.into());
        }

        Ok(())
    }

    /// 验证线性路由
    fn validate_linear(&self) -> Result<()> {
        // 线性路由的基本验证已在validate_continuity中完成
        Ok(())
    }

    /// 计算路由的复杂度评分
    pub fn complexity_score(&self) -> u8 {
        let base_score = self.routes.len() as u8;
        let mode_multiplier = match self.mode {
            RoutingMode::Linear => 1,
            RoutingMode::Circular => 2,
            RoutingMode::Branching => 3,
            RoutingMode::Batched => 4,
        };
        base_score * mode_multiplier
    }
}

//! 线性路由实现
//!
//! 处理 A -> B -> C 类型的顺序路由，是最基础的路由模式

use anchor_lang::prelude::*;
use crate::error::RouteError;
use super::types::*;
use super::executor::MultiHopRouteExecutor;

/// 线性路由执行器
pub struct LinearRouteExecutor;

impl LinearRouteExecutor {
    /// 执行线性路由
    ///
    /// # 参数
    /// * `routes` - 路由步骤列表，按执行顺序
    /// * `amount_in` - 初始输入数量
    /// * `remaining_accounts` - 所有需要的账户信息
    /// * `owner_seeds` - PDA签名种子（可选）
    ///
    /// # 返回
    /// 返回路由执行结果
    pub fn execute<'info>(
        routes: &[Route],
        amount_in: u64,
        remaining_accounts: &'info [AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        msg!("开始执行线性路由，步骤数: {}", routes.len());

        // 使用通用执行器执行路由序列
        let result = MultiHopRouteExecutor::execute_route_sequence(
            routes,
            amount_in,
            remaining_accounts,
            owner_seeds,
        )?;

        msg!("线性路由执行完成 - 最终输出: {}, Gas消耗: {}, 滑点: {}基点",
            result.amount_out, result.gas_used, result.actual_slippage_bps);

        Ok(result.amount_out)
    }

    /// 执行详细的线性路由（包含完整结果）
    pub fn execute_detailed<'info>(
        routes: &[Route],
        amount_in: u64,
        remaining_accounts: &'info [AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<RouteResult> {
        msg!("开始执行详细线性路由，步骤数: {}", routes.len());

        // 使用通用执行器执行路由序列
        let result = MultiHopRouteExecutor::execute_route_sequence(
            routes,
            amount_in,
            remaining_accounts,
            owner_seeds,
        )?;

        msg!("详细线性路由执行完成: {:?}", result);
        Ok(result)
    }

    /// 预验证线性路由的可行性
    pub fn validate_route_feasibility(
        routes: &[Route],
        amount_in: u64,
    ) -> Result<()> {
        // 验证基础路由配置
        if routes.is_empty() {
            return Err(RouteError::EmptyRoutePath.into());
        }

        if routes.len() > 6 {
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 验证路由连续性
        for i in 0..routes.len().saturating_sub(1) {
            if routes[i].output_mint != routes[i + 1].input_mint {
                return Err(RouteError::RouteDiscontinuity.into());
            }
        }

        // 验证流动性充足性
        MultiHopRouteExecutor::validate_route_liquidity(routes, amount_in)?;

        Ok(())
    }

    /// 估算线性路由的Gas消耗
    pub fn estimate_gas_cost(routes: &[Route]) -> u64 {
        let base_cost = 25_000; // 线性路由基础开销
        let per_step_cost = routes.iter().map(|route| {
            MultiHopRouteExecutor::estimate_step_gas(&route.dex)
        }).sum::<u64>();

        base_cost + per_step_cost
    }

    /// 预计算线性路由的输出
    pub fn calculate_output(
        routes: &[Route],
        amount_in: u64,
    ) -> Result<u64> {
        MultiHopRouteExecutor::estimate_total_output(routes, amount_in)
    }

    /// 获取线性路由的复杂度评分
    pub fn get_complexity_score(routes: &[Route]) -> u8 {
        MultiHopRouteExecutor::get_complexity_score(routes)
    }

    /// 验证线性路由的滑点容忍度
    pub fn validate_slippage_tolerance(
        routes: &[Route],
        amount_in: u64,
        max_slippage_bps: u16,
    ) -> Result<()> {
        let estimated_output = Self::calculate_output(routes, amount_in)?;

        // 计算每步的累积滑点影响
        let mut cumulative_slippage = 0u16;
        for route in routes {
            // 根据DEX类型估算滑点
            let step_slippage = match route.dex {
                Dex::RaydiumClmm => 15,    // CLMM滑点较低
                Dex::RaydiumCpmm => 25,    // CPMM滑点中等
                Dex::MeteoraAmm => 20,     // AMM滑点中等
                Dex::MeteoraLb => 10,      // LB滑点很低
                Dex::Orca => 30,  // Whirlpool滑点较高
                Dex::PumpSwap => 50,       // PumpSwap滑点最高
            };
            cumulative_slippage = cumulative_slippage.saturating_add(step_slippage);
        }

        if cumulative_slippage > max_slippage_bps {
            return Err(RouteError::SlippageTooHigh.into());
        }

        // 验证最终输出满足要求
        if let Some(last_route) = routes.last() {
            if estimated_output < last_route.min_amount_out {
                return Err(RouteError::InsufficientLiquidity.into());
            }
        }

        Ok(())
    }
}

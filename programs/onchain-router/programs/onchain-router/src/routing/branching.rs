//! 分支路由实现
//! 
//! 处理 A -> [B, C] -> D 类型的分散聚合路由
//! 将输入分散到多个路径，然后聚合到目标代币

use anchor_lang::prelude::*;
use crate::error::RouteError;
use super::types::*;
use super::executor::MultiHopRouteExecutor;

/// 分支路由执行器
pub struct BranchingRouteExecutor;

impl BranchingRouteExecutor {
    /// 执行分支路由
    /// 
    /// # 参数
    /// * `config` - 分支路由配置
    /// * `amount_in` - 初始输入数量
    /// * `remaining_accounts` - 所有需要的账户信息
    /// * `owner_seeds` - PDA签名种子（可选）
    /// 
    /// # 返回
    /// 返回聚合后的输出数量
    pub fn execute<'info>(
        config: &BranchRouteConfig,
        amount_in: u64,
        remaining_accounts: &'info [AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        msg!("开始执行分支路由 - 分支数: {}, 输入数量: {}", 
            config.branch_routes.len(), amount_in);
        
        // 1. 验证分支路由配置
        Self::validate_config(config)?;
        
        // 2. 根据分配比例分散输入资金
        let branch_amounts = Self::calculate_branch_amounts(config, amount_in)?;
        
        // 3. 执行各个分支路由
        let branch_results = Self::execute_branches(
            config,
            &branch_amounts,
            remaining_accounts,
            owner_seeds,
        )?;
        
        // 4. 聚合所有分支的输出
        let total_output = Self::aggregate_branch_outputs(&branch_results)?;
        
        msg!("分支路由执行完成 - 总输出: {}", total_output);
        Ok(total_output)
    }
    
    /// 验证分支路由配置
    pub fn validate_config(config: &BranchRouteConfig) -> Result<()> {
        // 检查分支数量限制
        if config.branch_routes.is_empty() {
            return Err(RouteError::EmptyRoutePath.into());
        }
        
        if config.branch_routes.len() > 8 {
            return Err(RouteError::RoutePathTooLong.into());
        }
        
        // 检查分配比例总和是否为100%
        let total_distribution: u16 = config.input_distribution.iter().sum();
        if total_distribution != 10000 {
            msg!("分配比例总和错误: {} != 10000", total_distribution);
            return Err(RouteError::InvalidRouteConfig.into());
        }
        
        // 检查分支数量匹配
        if config.input_distribution.len() != config.branch_routes.len() {
            msg!("分支数量不匹配: 分配 {} vs 路由 {}", 
                config.input_distribution.len(), config.branch_routes.len());
            return Err(RouteError::InvalidRouteConfig.into());
        }
        
        // 验证每个分支路由
        for (i, branch) in config.branch_routes.iter().enumerate() {
            if branch.is_empty() {
                msg!("分支 {} 为空", i);
                return Err(RouteError::EmptyRoutePath.into());
            }
            
            if branch.len() > 4 {
                msg!("分支 {} 步骤过多: {}", i, branch.len());
                return Err(RouteError::RoutePathTooLong.into());
            }
            
            // 验证分支内部连续性
            for j in 0..branch.len().saturating_sub(1) {
                if branch[j].output_mint != branch[j + 1].input_mint {
                    msg!("分支 {} 步骤 {} 不连续", i, j);
                    return Err(RouteError::RouteDiscontinuity.into());
                }
            }
            
            // 验证分支的最终输出都是目标代币
            if let Some(last_route) = branch.last() {
                if last_route.output_mint != config.target_mint {
                    msg!("分支 {} 最终输出 {} != 目标 {}", 
                        i, last_route.output_mint, config.target_mint);
                    return Err(RouteError::RouteDiscontinuity.into());
                }
            }
            
            // 验证每个路由步骤
            for route in branch {
                route.validate()?;
            }
        }
        
        msg!("分支路由配置验证通过");
        Ok(())
    }
    
    /// 计算各分支的输入数量
    fn calculate_branch_amounts(
        config: &BranchRouteConfig,
        amount_in: u64,
    ) -> Result<Vec<u64>> {
        let mut branch_amounts = Vec::new();
        let mut remaining_amount = amount_in;
        
        for (i, &distribution_bps) in config.input_distribution.iter().enumerate() {
            let branch_amount = if i == config.input_distribution.len() - 1 {
                // 最后一个分支使用剩余的全部数量，避免舍入误差
                remaining_amount
            } else {
                amount_in
                    .checked_mul(distribution_bps as u64)
                    .ok_or(RouteError::MathOverflow)?
                    .checked_div(10000)
                    .ok_or(RouteError::DivisionByZero)?
            };
            
            if branch_amount == 0 {
                return Err(RouteError::AmountValidationFailed.into());
            }
            
            branch_amounts.push(branch_amount);
            remaining_amount = remaining_amount.saturating_sub(branch_amount);
            
            msg!("分支 {} 分配数量: {} ({} 基点)", 
                i + 1, branch_amount, distribution_bps);
        }
        
        Ok(branch_amounts)
    }
    
    /// 执行所有分支路由
    fn execute_branches<'info>(
        config: &BranchRouteConfig,
        branch_amounts: &[u64],
        remaining_accounts: &'info [AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<Vec<RouteResult>> {
        let mut results = Vec::new();
        let mut account_offset = 0;
        
        for (i, (branch_routes, &amount)) in config.branch_routes.iter()
            .zip(branch_amounts.iter()).enumerate() {
            
            msg!("执行分支 {} - 路由步骤: {}, 输入: {}", 
                i + 1, branch_routes.len(), amount);
            
            // 计算当前分支需要的账户数量
            let accounts_needed = Self::calculate_branch_accounts_needed(branch_routes);
            
            if account_offset + accounts_needed > remaining_accounts.len() {
                return Err(RouteError::InvalidDexAccounts.into());
            }
            
            let branch_accounts = &remaining_accounts[account_offset..account_offset + accounts_needed];
            
            // 执行分支路由
            let result = MultiHopRouteExecutor::execute_route_sequence(
                branch_routes,
                amount,
                branch_accounts,
                owner_seeds,
            )?;
            
            // 验证分支输出代币类型
            if let Some(last_route) = branch_routes.last() {
                if last_route.output_mint != config.target_mint {
                    return Err(RouteError::RouteDiscontinuity.into());
                }
            }
            
            msg!("分支 {} 完成 - 输出: {}, Gas: {}", 
                i + 1, result.amount_out, result.gas_used);
            
            results.push(result);
            account_offset += accounts_needed;
        }
        
        Ok(results)
    }
    
    /// 聚合分支输出
    fn aggregate_branch_outputs(results: &[RouteResult]) -> Result<u64> {
        let mut total_output = 0u64;
        
        for (i, result) in results.iter().enumerate() {
            total_output = total_output
                .checked_add(result.amount_out)
                .ok_or(RouteError::MathOverflow)?;
            
            msg!("聚合分支 {} 输出: {}, 累计: {}", 
                i + 1, result.amount_out, total_output);
        }
        
        if total_output == 0 {
            return Err(RouteError::AmountValidationFailed.into());
        }
        
        Ok(total_output)
    }
    
    /// 计算分支所需的账户数量
    fn calculate_branch_accounts_needed(branch_routes: &[Route]) -> usize {
        branch_routes.iter().map(|route| {
            match route.dex {
                Dex::RaydiumClmm => 12,
                Dex::RaydiumCpmm => 10,
                Dex::MeteoraAmm => 9,
                Dex::MeteoraLb => 14,
                Dex::Orca => 11,
                Dex::PumpSwap => 8,
            }
        }).sum()
    }
    
    /// 执行详细的分支路由（包含完整结果）
    pub fn execute_detailed<'info>(
        config: &BranchRouteConfig,
        amount_in: u64,
        remaining_accounts: &'info [AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<BranchRouteResult> {
        msg!("开始执行详细分支路由");
        
        // 验证配置
        Self::validate_config(config)?;
        
        // 计算分支数量
        let branch_amounts = Self::calculate_branch_amounts(config, amount_in)?;
        
        // 执行分支
        let branch_results = Self::execute_branches(
            config,
            &branch_amounts,
            remaining_accounts,
            owner_seeds,
        )?;
        
        // 聚合结果
        let total_output = Self::aggregate_branch_outputs(&branch_results)?;
        
        let total_gas = branch_results.iter().map(|r| r.gas_used).sum();
        let avg_slippage = Self::calculate_average_slippage(&branch_results);
        
        let result = BranchRouteResult {
            total_output,
            branch_results,
            branch_amounts,
            total_gas_used: total_gas,
            average_slippage_bps: avg_slippage,
        };
        
        msg!("详细分支路由执行完成: {:?}", result);
        Ok(result)
    }
    
    /// 计算平均滑点
    fn calculate_average_slippage(results: &[RouteResult]) -> u16 {
        if results.is_empty() {
            return 0;
        }
        
        let total_slippage: u32 = results.iter()
            .map(|r| r.actual_slippage_bps as u32)
            .sum();
        
        (total_slippage / results.len() as u32) as u16
    }
    
    /// 估算分支路由的Gas消耗
    pub fn estimate_gas_cost(config: &BranchRouteConfig) -> u64 {
        let base_cost = 40_000; // 分支路由的基础开销
        let branch_costs: u64 = config.branch_routes.iter()
            .map(|branch| {
                let per_step_cost = branch.iter()
                    .map(|route| MultiHopRouteExecutor::estimate_step_gas(&route.dex))
                    .sum::<u64>();
                per_step_cost + 10_000 // 分支执行开销
            })
            .sum();
            
        base_cost + branch_costs
    }
    
    /// 预估分支路由的总输出
    pub fn estimate_total_output(
        config: &BranchRouteConfig,
        amount_in: u64,
    ) -> Result<u64> {
        let branch_amounts = Self::calculate_branch_amounts(config, amount_in)?;
        let mut total_estimated_output = 0u64;
        
        for (branch_routes, &amount) in config.branch_routes.iter()
            .zip(branch_amounts.iter()) {
            
            let branch_output = MultiHopRouteExecutor::estimate_total_output(
                branch_routes, 
                amount
            )?;
            
            total_estimated_output = total_estimated_output
                .checked_add(branch_output)
                .ok_or(RouteError::MathOverflow)?;
        }
        
        Ok(total_estimated_output)
    }
}
use anchor_lang::prelude::*;

/// 路由器错误类型
/// 遵循分类和层次化的错误设计，便于调试和错误处理
#[error_code]
pub enum RouteError {
    // === 路由配置相关错误 (6000-6099) ==
    #[msg("无效的路由配置")]
    InvalidRouteConfig = 6000,

    #[msg("路由路径为空")]
    EmptyRoutePath = 6001,

    #[msg("路由路径过长，超过最大允许步骤数")]
    RoutePathTooLong = 6002,

    #[msg("路由路径不连续，相邻步骤的代币不匹配")]
    RouteDiscontinuity = 6003,

    #[msg("循环路由验证失败，起始和结束代币不匹配")]
    NotCircularRoute = 6004,

    #[msg("无效的路由模式")]
    InvalidRoutingMode = 6005,

    // === DEX操作相关错误 (6100-6199) ===
    #[msg("不支持的DEX协议")]
    UnsupportedDex = 6100,

    #[msg("DEX适配器执行失败")]
    DexAdapterFailed = 6101,

    #[msg("流动性不足")]
    InsufficientLiquidity = 6102,

    #[msg("滑点超过允许范围")]
    SlippageTooHigh = 6103,

    #[msg("价格影响超过允许范围")]
    PriceImpactTooHigh = 6104,

    #[msg("DEX账户验证失败")]
    InvalidDexAccounts = 6105,

    #[msg("DEX程序调用失败")]
    DexCpiCallFailed = 6106,

    // === 闪电贷相关错误 (6200-6299) ===
    #[msg("闪电贷提供者不可用")]
    FlashLoanProviderUnavailable = 6200,

    #[msg("闪电贷金额超过限制")]
    FlashLoanAmountExceeded = 6201,

    #[msg("闪电贷费用计算错误")]
    FlashLoanFeeCalculationError = 6202,

    #[msg("闪电贷回调执行失败")]
    FlashLoanCallbackFailed = 6203,

    #[msg("闪电贷偿还失败")]
    FlashLoanRepaymentFailed = 6204,

    #[msg("未授权的闪电贷提供者")]
    UnauthorizedFlashLoanProvider = 6205,

    #[msg("利润低于阈值")]
    ProfitBelowThreshold = 6206,

    #[msg("Gas费用过高")]
    GasFeeTooHigh = 6207,

    #[msg("闪电贷费率过高")]
    FlashLoanFeeTooHigh = 6208,

    #[msg("缺少抵押品账户")]
    MissingCollateralAccount = 6209,

    // 新增的闪电贷相关错误 (6210-6249)
    #[msg("无效的闪电贷金额")]
    InvalidFlashLoanAmount = 6210,

    #[msg("不支持的代币类型")]
    UnsupportedMint = 6211,

    #[msg("闪电贷执行失败")]
    FlashLoanExecutionFailed = 6212,

    #[msg("闪电贷费用不正确")]
    IncorrectFlashLoanFee = 6213,

    #[msg("算术计算溢出")]
    ArithmeticOverflow = 6214,

    #[msg("账户数量不足")]
    InsufficientAccounts = 6215,

    #[msg("无效的程序ID")]
    InvalidProgram = 6216,

    #[msg("无效的池地址")]
    InvalidPoolAddress = 6217,

    #[msg("无效的池所有者")]
    InvalidPoolOwner = 6218,

    #[msg("闪电贷提供者不存在")]
    ProviderNotFound = 6219,

    #[msg("闪电贷提供者已存在")]
    ProviderAlreadyExists = 6220,

    #[msg("没有可用的提供者")]
    NoAvailableProvider = 6221,

    #[msg("没有合适的提供者")]
    NoSuitableProvider = 6222,

    #[msg("不支持的提供者")]
    UnsupportedProvider = 6223,

    #[msg("套利执行失败")]
    ArbitrageExecutionFailed = 6224,

    #[msg("利润不足")]
    InsufficientProfit = 6225,

    #[msg("无效的回调数据")]
    InvalidCallbackData = 6226,

    #[msg("回调数据过大")]
    CallbackDataTooLarge = 6227,

    #[msg("回调超时")]
    CallbackTimeout = 6228,

    #[msg("无效的预期利润")]
    InvalidExpectedProfit = 6229,

    #[msg("利润容忍度过高")]
    ProfitToleranceTooHigh = 6230,

    #[msg("无效的优先级")]
    InvalidPriority = 6231,

    // === 安全控制相关错误 (6300-6399) ===
    #[msg("全局紧急停止已启动")]
    GlobalEmergencyStop = 6300,

    #[msg("DEX紧急停止已启动")]
    DexEmergencyStop = 6301,

    #[msg("用户被暂停")]
    UserSuspended = 6302,

    #[msg("重入攻击检测")]
    ReentrancyDetected = 6303,

    #[msg("金额验证失败")]
    AmountValidationFailed = 6304,

    #[msg("权限验证失败")]
    PermissionDenied = 6305,

    #[msg("风险评分过高")]
    RiskScoreTooHigh = 6306,

    #[msg("路由复杂度过高")]
    RouteComplexityTooHigh = 6307,

    #[msg("原子性执行失败")]
    AtomicExecutionFailed = 6308,

    // === 账户相关错误 (6400-6499) ===
    #[msg("无效的交换权限账户")]
    InvalidSwapAuthority = 6400,

    #[msg("交换权限账户未签名")]
    SwapAuthorityIsNotSigner = 6401,

    #[msg("代币账户所有者不匹配")]
    TokenAccountOwnerMismatch = 6402,

    #[msg("代币账户mint不匹配")]
    TokenAccountMintMismatch = 6403,

    #[msg("余额不足")]
    InsufficientBalance = 6404,

    // === 计算相关错误 (6500-6599) ===
    #[msg("数学计算溢出")]
    MathOverflow = 6500,

    #[msg("数学计算下溢")]
    MathUnderflow = 6501,

    #[msg("除零错误")]
    DivisionByZero = 6502,

    #[msg("价格计算错误")]
    PriceCalculationError = 6503,

    #[msg("利润计算错误")]
    ProfitCalculationError = 6504,

    // === 一般性错误 (6900-6999) ===
    #[msg("未知错误")]
    UnknownError = 6900,

    #[msg("操作超时")]
    OperationTimeout = 6901,

    #[msg("配置未初始化")]
    ConfigNotInitialized = 6902,

    #[msg("无效的操作序列")]
    InvalidOperationSequence = 6903,

    #[msg("DEX操作失败")]
    DexOperationFailed = 6904,

    #[msg("无效的账户数量")]
    InvalidAccountCount = 6905,

    // === 套利相关错误 (6950-6999) ===

    #[msg("无效的套利路径")]
    InvalidArbitragePath = 6952,

    #[msg("无效的超时设置")]
    InvalidTimeout = 6953,

    #[msg("滑点超过限制")]
    SlippageExceeded = 6954,

    #[msg("无效的分配金额")]
    InvalidDistributionAmount = 6955,

    #[msg("用户利润不足")]
    InsufficientUserProfit = 6956,

    #[msg("协议费用过高")]
    ExcessiveProtocolFee = 6957,

    #[msg("费率过高")]
    ExcessiveFeeRate = 6958,

    #[msg("无效的Mint账户")]
    InvalidMintAccount = 6959,

    #[msg("每日交易量限制已超过")]
    DailyVolumeLimitExceeded = 6960,

    #[msg("交易金额超过限制")]
    TradingAmountExceeded = 6961,

    #[msg("风险评估失败")]
    RiskAssessmentFailed = 6962,
}

/// 错误结果类型别名
pub type RouteResult<T> = std::result::Result<T, RouteError>;

/// 带错误上下文的结果类型
#[derive(Debug)]
pub struct RouteErrorContext {
    pub error: RouteError,
    pub context: String,
    pub user: Option<Pubkey>,
    pub order_id: Option<u64>,
    pub timestamp: i64,
}

impl RouteErrorContext {
    pub fn new(
        error: RouteError,
        context: impl Into<String>,
        user: Option<Pubkey>,
        order_id: Option<u64>,
    ) -> Self {
        Self {
            error,
            context: context.into(),
            user,
            order_id,
            timestamp: Clock::get().map(|c| c.unix_timestamp).unwrap_or(0),
        }
    }

    pub fn log(&self) {
        log_route_error(&self.error, &self.context, self.user.as_ref(), self.order_id);
    }
}

/// 从其他错误类型转换为RouteError的便捷宏
#[macro_export]
macro_rules! route_error {
    ($err:expr) => {
        Err(RouteError::from($err))
    };
}

/// 检查条件，如果失败则返回指定的RouteError
#[macro_export]
macro_rules! require_route {
    ($condition:expr, $error:expr) => {
        if !($condition) {
            return Err($error.into());
        }
    };
    ($condition:expr, $error:expr, $context:expr) => {
        if !($condition) {
            crate::error::log_route_error(&$error, $context, None, None);
            return Err($error.into());
        }
    };
}

/// 带上下文的错误记录宏
#[macro_export]
macro_rules! route_error_with_context {
    ($error:expr, $context:expr) => {{
        let error_ctx = crate::error::RouteErrorContext::new(
            $error,
            $context,
            None,
            None,
        );
        error_ctx.log();
        Err($error.into())
    }};
    ($error:expr, $context:expr, $user:expr, $order_id:expr) => {{
        let error_ctx = crate::error::RouteErrorContext::new(
            $error,
            $context,
            Some($user),
            Some($order_id),
        );
        error_ctx.log();
        Err($error.into())
    }};
}

/// 用于将第三方错误转换为RouteError
impl From<anchor_lang::error::Error> for RouteError {
    fn from(_error: anchor_lang::error::Error) -> Self {
        // 简化错误转换，因为新版本的anchor_lang::error::Error变体已变更
        RouteError::UnknownError
    }
}


/// 将程序错误转换为RouteError
impl From<ProgramError> for RouteError {
    fn from(error: ProgramError) -> Self {
        match error {
            ProgramError::InsufficientFunds => RouteError::InsufficientBalance,
            ProgramError::InvalidAccountData => RouteError::InvalidDexAccounts,
            ProgramError::InvalidArgument => RouteError::InvalidRouteConfig,
            ProgramError::InvalidAccountOwner => RouteError::TokenAccountOwnerMismatch,
            ProgramError::AccountDataTooSmall => RouteError::InvalidDexAccounts,
            ProgramError::MissingRequiredSignature => RouteError::SwapAuthorityIsNotSigner,
            ProgramError::ArithmeticOverflow => RouteError::MathOverflow,
            _ => RouteError::UnknownError,
        }
    }
}


/// 通用的DEX错误转换trait
pub trait ToRouteError {
    fn to_route_error(self) -> RouteError;
}

/// 错误代码转换为RouteError
pub fn convert_dex_error_code(error_code: u32, dex_name: &str) -> RouteError {
    match dex_name {
        "Raydium" => match error_code {
            6000..=6009 => RouteError::InsufficientLiquidity,
            6010..=6019 => RouteError::SlippageTooHigh,
            6020..=6029 => RouteError::InvalidDexAccounts,
            _ => RouteError::DexAdapterFailed,
        },
        "Meteora" => match error_code {
            6000..=6009 => RouteError::InsufficientLiquidity,
            6010..=6019 => RouteError::SlippageTooHigh,
            6020..=6029 => RouteError::InvalidDexAccounts,
            _ => RouteError::DexAdapterFailed,
        },
        "Orca" => match error_code {
            6000..=6009 => RouteError::InsufficientLiquidity,
            6010..=6019 => RouteError::SlippageTooHigh,
            6020..=6029 => RouteError::InvalidDexAccounts,
            _ => RouteError::DexAdapterFailed,
        },
        "PumpSwap" => match error_code {
            6000..=6009 => RouteError::InsufficientLiquidity,
            6010..=6019 => RouteError::SlippageTooHigh,
            6020..=6029 => RouteError::InvalidDexAccounts,
            _ => RouteError::DexAdapterFailed,
        },
        _ => RouteError::UnsupportedDex,
    }
}

/// 错误日志记录工具
pub fn log_route_error(
    error: &RouteError,
    context: &str,
    user: Option<&Pubkey>,
    order_id: Option<u64>,
) {
    let user_str = user.map(|u| u.to_string()).unwrap_or_else(|| "Unknown".to_string());
    let order_str = order_id.map(|id| id.to_string()).unwrap_or_else(|| "None".to_string());

    msg!(
        "RouteError[{}]: {} | Context: {} | User: {} | OrderID: {} | Time: {}",
        *error as u32,
        error,
        context,
        user_str,
        order_str,
        Clock::get().map(|c| c.unix_timestamp).unwrap_or(0)
    );
}

/// 检查是否为可重试的错误
pub fn is_retryable_error(error: &RouteError) -> bool {
    matches!(
        error,
        RouteError::InsufficientLiquidity
            | RouteError::SlippageTooHigh
            | RouteError::DexCpiCallFailed
            | RouteError::OperationTimeout
    )
}

/// 检查是否为致命错误（需要立即停止）
pub fn is_fatal_error(error: &RouteError) -> bool {
    matches!(
        error,
        RouteError::GlobalEmergencyStop
            | RouteError::UserSuspended
            | RouteError::ReentrancyDetected
            | RouteError::PermissionDenied
    )
}

/// 获取错误的严重程度级别
pub fn get_error_severity(error: &RouteError) -> u8 {
    match error {
        // 低严重程度 (1-3)
        RouteError::SlippageTooHigh
        | RouteError::InsufficientLiquidity
        | RouteError::OperationTimeout => 2,

        // 中等严重程度 (4-6)
        RouteError::InvalidRouteConfig
        | RouteError::RoutePathTooLong
        | RouteError::AmountValidationFailed
        | RouteError::DexAdapterFailed => 4,

        // 高严重程度 (7-9)
        RouteError::PermissionDenied
        | RouteError::TokenAccountOwnerMismatch
        | RouteError::DexEmergencyStop => 7,

        // 极高严重程度 (10)
        RouteError::GlobalEmergencyStop
        | RouteError::ReentrancyDetected
        | RouteError::UserSuspended => 10,

        _ => 5, // 默认中等严重程度
    }
}

/// 错误恢复动作
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ErrorRecoveryAction {
    /// 立即重试
    Retry,
    /// 延迟重试（毫秒）
    RetryWithDelay(u64),
    /// 使用备用路由
    UseAlternativeRoute,
    /// 减少交易金额（百分比）
    ReduceAmount(u8),
    /// 跳过当前步骤
    SkipStep,
    /// 切换到另一个DEX
    SwitchDex,
    /// 中止路由执行
    AbortRoute,
}

/// 错误恢复策略
pub trait ErrorRecovery {
    fn can_retry(&self) -> bool;
    fn get_retry_delay(&self) -> Option<u64>;
    fn get_recovery_action(&self, attempt_count: u8) -> Option<ErrorRecoveryAction>;
    fn get_max_attempts(&self) -> u8;
}

impl ErrorRecovery for RouteError {
    fn can_retry(&self) -> bool {
        !is_fatal_error(self) && is_retryable_error(self)
    }

    fn get_retry_delay(&self) -> Option<u64> {
        match self {
            RouteError::SlippageTooHigh => Some(1000),
            RouteError::OperationTimeout => Some(500),
            RouteError::DexCpiCallFailed => Some(300),
            RouteError::InsufficientLiquidity => Some(2000),
            _ => Some(100),
        }
    }

    fn get_recovery_action(&self, attempt_count: u8) -> Option<ErrorRecoveryAction> {
        if attempt_count >= self.get_max_attempts() {
            return Some(ErrorRecoveryAction::AbortRoute);
        }

        match self {
            RouteError::InsufficientLiquidity => {
                if attempt_count == 0 {
                    Some(ErrorRecoveryAction::SwitchDex)
                } else {
                    Some(ErrorRecoveryAction::ReduceAmount(10))
                }
            },
            RouteError::SlippageTooHigh => Some(ErrorRecoveryAction::RetryWithDelay(1000)),
            RouteError::PriceImpactTooHigh => Some(ErrorRecoveryAction::ReduceAmount(20)),
            RouteError::DexCpiCallFailed => Some(ErrorRecoveryAction::Retry),
            RouteError::OperationTimeout => Some(ErrorRecoveryAction::RetryWithDelay(500)),
            RouteError::RoutePathTooLong => Some(ErrorRecoveryAction::UseAlternativeRoute),
            RouteError::DexAdapterFailed => Some(ErrorRecoveryAction::SwitchDex),

            // 闪电贷相关错误的恢复策略
            RouteError::FlashLoanProviderUnavailable => Some(ErrorRecoveryAction::SwitchDex),
            RouteError::NoSuitableProvider => Some(ErrorRecoveryAction::RetryWithDelay(2000)),
            RouteError::FlashLoanExecutionFailed => {
                if attempt_count == 0 {
                    Some(ErrorRecoveryAction::SwitchDex)
                } else {
                    Some(ErrorRecoveryAction::AbortRoute)
                }
            },
            RouteError::ArbitrageExecutionFailed => Some(ErrorRecoveryAction::UseAlternativeRoute),
            RouteError::InsufficientUserProfit => Some(ErrorRecoveryAction::ReduceAmount(5)),
            RouteError::CallbackTimeout => Some(ErrorRecoveryAction::RetryWithDelay(1500)),

            // 致命错误，无法恢复
            RouteError::GlobalEmergencyStop
            | RouteError::UserSuspended
            | RouteError::ReentrancyDetected
            | RouteError::PermissionDenied
            | RouteError::InvalidFlashLoanAmount
            | RouteError::UnsupportedMint
            | RouteError::InvalidCallbackData => None,

            _ => {
                if attempt_count == 0 {
                    Some(ErrorRecoveryAction::Retry)
                } else {
                    Some(ErrorRecoveryAction::AbortRoute)
                }
            }
        }
    }

    fn get_max_attempts(&self) -> u8 {
        match self {
            RouteError::InsufficientLiquidity => 3,
            RouteError::SlippageTooHigh => 5,
            RouteError::DexCpiCallFailed => 3,
            RouteError::OperationTimeout => 2,
            RouteError::GlobalEmergencyStop
            | RouteError::UserSuspended
            | RouteError::ReentrancyDetected
            | RouteError::PermissionDenied => 0,
            _ => 2,
        }
    }
}

/// 扩展的错误统计结构
#[derive(Debug, Clone)]
pub struct ErrorStatistics {
    pub total_errors: u64,
    pub retryable_errors: u64,
    pub fatal_errors: u64,
    pub recovered_errors: u64,
    pub severity_counts: [u64; 11], // 0-10严重程度级别
    // 暂时移除HashMap以避免no_std环境问题
    // pub error_type_counts: std::collections::HashMap<u32, u64>,
    pub recovery_success_rate: f64,
    pub last_error_timestamp: i64,
    pub error_burst_count: u64, // 短时间内的错误计数
    pub burst_window_start: i64, // 错误爆发窗口开始时间
}

impl ErrorStatistics {
    pub fn new() -> Self {
        Self {
            total_errors: 0,
            retryable_errors: 0,
            fatal_errors: 0,
            recovered_errors: 0,
            severity_counts: [0; 11],
            // error_type_counts: std::collections::HashMap::new(),
            recovery_success_rate: 0.0,
            last_error_timestamp: 0,
            error_burst_count: 0,
            burst_window_start: 0,
        }
    }

    pub fn record_error(&mut self, error: &RouteError) {
        let current_time = Clock::get().map(|c| c.unix_timestamp).unwrap_or(0);

        self.total_errors += 1;
        self.last_error_timestamp = current_time;

        // 记录错误类型计数 - 暂时移除以避免HashMap问题
        // let error_code = *error as u32;
        // *self.error_type_counts.entry(error_code).or_insert(0) += 1;

        if is_retryable_error(error) {
            self.retryable_errors += 1;
        }

        if is_fatal_error(error) {
            self.fatal_errors += 1;
        }

        let severity = get_error_severity(error) as usize;
        if severity <= 10 {
            self.severity_counts[severity] += 1;
        }

        // 检查错误爆发
        self.check_error_burst(current_time);
    }

    pub fn record_recovery(&mut self, success: bool) {
        if success {
            self.recovered_errors += 1;
        }

        // 更新恢复成功率
        if self.retryable_errors > 0 {
            self.recovery_success_rate = (self.recovered_errors as f64) / (self.retryable_errors as f64);
        }
    }

    pub fn fatal_error_rate(&self) -> f64 {
        if self.total_errors == 0 {
            0.0
        } else {
            (self.fatal_errors as f64) / (self.total_errors as f64)
        }
    }

    pub fn get_most_common_error(&self) -> Option<(u32, u64)> {
        // 暂时返回None，因为移除了HashMap
        None
        // self.error_type_counts
        //     .iter()
        //     .max_by_key(|(_, count)| *count)
        //     .map(|(code, count)| (*code, *count))
    }

    pub fn is_error_burst_detected(&self) -> bool {
        self.error_burst_count >= 10 // 在5分钟窗口内超过10个错误视为爆发
    }

    fn check_error_burst(&mut self, current_time: i64) {
        const BURST_WINDOW_SECONDS: i64 = 300; // 5分钟窗口

        // 如果距离上次窗口开始超过5分钟，重置计数器
        if current_time - self.burst_window_start > BURST_WINDOW_SECONDS {
            self.burst_window_start = current_time;
            self.error_burst_count = 1;
        } else {
            self.error_burst_count += 1;
        }
    }

    pub fn get_error_health_score(&self) -> u8 {
        let fatal_rate = self.fatal_error_rate();
        let recovery_rate = self.recovery_success_rate;
        let burst_penalty = if self.is_error_burst_detected() { 0.3 } else { 0.0 };

        let base_score = 100.0 - (fatal_rate * 50.0) + (recovery_rate * 20.0) - (burst_penalty * 100.0);
        base_score.max(0.0).min(100.0) as u8
    }
}

/// 错误恢复会话
#[derive(Debug, Clone)]
pub struct ErrorRecoverySession {
    pub error: RouteError,
    pub attempt_count: u8,
    pub max_attempts: u8,
    pub last_attempt_time: i64,
    pub recovery_actions_tried: Vec<ErrorRecoveryAction>,
    pub success: bool,
}

impl ErrorRecoverySession {
    pub fn new(error: RouteError) -> Self {
        let max_attempts = error.get_max_attempts();
        Self {
            error,
            attempt_count: 0,
            max_attempts,
            last_attempt_time: Clock::get().map(|c| c.unix_timestamp).unwrap_or(0),
            recovery_actions_tried: Vec::new(),
            success: false,
        }
    }

    pub fn can_retry(&self) -> bool {
        self.attempt_count < self.max_attempts && self.error.can_retry()
    }

    pub fn get_next_action(&self) -> Option<ErrorRecoveryAction> {
        self.error.get_recovery_action(self.attempt_count)
    }

    pub fn record_attempt(&mut self, action: ErrorRecoveryAction) {
        self.attempt_count += 1;
        self.last_attempt_time = Clock::get().map(|c| c.unix_timestamp).unwrap_or(0);
        self.recovery_actions_tried.push(action);
    }

    pub fn mark_success(&mut self) {
        self.success = true;
    }

    pub fn should_delay(&self) -> Option<u64> {
        if self.attempt_count > 0 {
            self.error.get_retry_delay()
        } else {
            None
        }
    }
}
